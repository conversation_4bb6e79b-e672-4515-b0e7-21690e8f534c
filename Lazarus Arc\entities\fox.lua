-- entities/fox.lua

local Fox = {
    id = "fox",
    name = "<PERSON>",
    type = "fox",
    shape = {
        {0, -1}, {0.7, -0.7}, {1, 0}, {0.7, 0.7},
        {0, 1}, {-0.7, 0.7}, {-1, 0}, {-0.7, -0.7}
    },
    size = 7,

    -- Entity categories for relationship handling
    categories = {"animal", "predator", "mammal", "medium"},

    -- Threat and food categories
    threatCategories = {"monster", "player"},
    foodCategories = {"small_prey", "livestock"},

    -- Stats
    maxHealth = 35,
    health = 35,
    maxStamina = 50,
    stamina = 50,
    speed = 2.5, -- Foxes are quite fast

    -- Behaviors
    behaviors = {"hunt", "wander", "scavenge"},

    -- Behavior configurations
    behaviorConfigs = {
        hunt = {
            moveSpeed = 3.0,
            huntRadius = 12,
            chaseRadius = 15,
            attackRange = 1.5,
            attackDamage = 10,
            attackCooldown = 1.0,
            preferredTargets = {"rabbit", "field_mouse", "bird"}
        },
        wander = {
            moveSpeed = 1.5,
            changeDirectionChance = 0.03,
            wanderRadius = 8
        },
        scavenge = {
            moveSpeed = 1.0,
            searchRadius = 10,
            foodTypes = {"meat", "carrion"}
        }
    },

    -- Special abilities
    abilities = {
        pounce = {
            damageMultiplier = 1.5,
            range = 3,
            cooldown = 5
        },
        stealth = {
            detectionReduction = 0.5,
            duration = 3,
            cooldown = 8
        }
    },

    -- Appearance
    appearance = {
        sprite = "fox",
        scale = 1.0,
        animations = {
            "idle", "walk", "run", "pounce", "stealth"
        },
        variants = {
            "red", "gray", "arctic", "desert"
        }
    },

    -- Sound effects
    sounds = {
        bark = "fox_bark",
        growl = "fox_growl",
        yip = "fox_yip"
    },

    -- Loot drops
    drops = {
        {id = "meat", chance = 0.8, quantity = {1, 2}},
        {id = "hide", chance = 0.7, quantity = {1, 1}},
        {id = "tail", chance = 0.4, quantity = {1, 1}},
        {id = "fur", chance = 0.6, quantity = {1, 2}},
        {id = "fang", chance = 0.3, quantity = {1, 1}}
    }
}

-- Initialize the entity
function Fox.init(entity, world)
    -- Copy all fields from Fox template to entity instance
    for k, v in pairs(Fox) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity if not provided
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random fox variant (with safety checks)
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

return Fox