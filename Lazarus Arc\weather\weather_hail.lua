-- weather/hail.lua
-- Hail weather pattern - damaging ice projectiles and freezing conditions

local HailWeather = {
    id = "hail",
    name = "Hail Storm",
    
    -- Visual properties
    visual = {
        skyColor = {r = 100, g = 100, b = 120}, -- Dark gray
        sunIntensity = 0.3,
        cloudCoverage = 1.0,
        cloudColor = {r = 80, g = 80, b = 100}, -- Dark storm clouds
        ambientLightLevel = 0.4,
        hailColor = {r = 200, g = 200, b = 255} -- Ice crystals
    },
    
    -- Environment modifiers
    environment = {
        temperature = 0.3, -- Very cold
        visibility = 0.6, -- Reduced visibility
        humidity = 0.8, -- High humidity
        windStrength = 0.7, -- Strong wind
        iceDensity = 0.8, -- High ice concentration
        freezingLevel = 0.9 -- High freezing potential
    },
    
    -- Particle systems
    particles = {
        hailStones = {
            enabled = true,
            intensity = 0.8,
            color = {r = 200, g = 200, b = 255},
            size = {min = 2, max = 6},
            speed = {min = 400, max = 600}
        },
        iceFragments = {
            enabled = true,
            intensity = 0.5,
            color = {r = 180, g = 180, b = 220},
            size = {min = 1, max = 3},
            speed = {min = 300, max = 500}
        },
        snowFlurries = {
            enabled = true,
            intensity = 0.3,
            color = {r = 220, g = 220, b = 255},
            size = {min = 1, max = 2},
            speed = {min = 50, max = 100}
        }
    },
    
    -- Sound effects
    sounds = {
        ambient = "hail_ambient",
        impact = "hail_impact",
        wind = "cold_wind",
        iceBreak = "ice_shatter",
        volume = 0.8
    },
    
    -- Effects on game entities and tiles
    effects = {
        -- Damage to exposed entities
        exposureDamage = 4, -- Damage per second
        
        -- Equipment damage
        equipmentDamageRate = 1.5,
        
        -- Movement effects
        movementSpeedMultiplier = 0.6,
        
        -- Vision effects
        visionRange = 0.6,
        
        -- Freezing effects
        freezingDamage = 2,
        
        -- Ice accumulation
        iceBuildUpRate = 0.5,
        
        -- Slip chance
        slipChance = 0.2,
        
        -- Shield effects
        shieldEffectiveness = 0.7
    },
    
    -- Transition probabilities to other weather (per game hour)
    transitions = {
        clear = 0.2,
        cloudy = 0.3,
        rain = 0.2,
        snow = 0.1,
        -- Implied: 0.2 chance to stay hail
    },
    
    -- Day/night cycle modifiers
    timeModifiers = {
        dawn = {
            skyColor = {r = 120, g = 120, b = 140},
            ambientLightLevel = 0.3
        },
        day = {
            -- Uses default properties
        },
        dusk = {
            skyColor = {r = 90, g = 90, b = 110},
            ambientLightLevel = 0.2
        },
        night = {
            skyColor = {r = 50, g = 50, b = 70},
            ambientLightLevel = 0.1
        }
    }
}

-- Initialize the weather pattern
function HailWeather.init(world)
    print("Initializing hail weather")
    
    -- Set global light level
    world.lightLevel = HailWeather.visual.ambientLightLevel
    
    -- Start ambient sounds
    if HailWeather.sounds.ambient then
        -- Play ambient hail sound
        print("Playing sound: " .. HailWeather.sounds.ambient)
    end
    
    -- Create particle systems
    if HailWeather.particles.hailStones.enabled then
        -- Create hail stone particles
        print("Creating hail stone particles with intensity: " .. HailWeather.particles.hailStones.intensity)
    end
    
    if HailWeather.particles.iceFragments.enabled then
        -- Create ice fragment particles
        print("Creating ice fragment particles with intensity: " .. HailWeather.particles.iceFragments.intensity)
    end
    
    if HailWeather.particles.snowFlurries.enabled then
        -- Create snow flurry particles
        print("Creating snow flurry particles with intensity: " .. HailWeather.particles.snowFlurries.intensity)
    end
    
    -- Display warning message to players if message system exists
    if world.messageSystem then
        world.messageSystem:broadcast("WARNING: Hail storm approaching. Seek shelter and protect equipment.")
    else
        print("WARNING: Hail storm approaching. Seek shelter and protect equipment.")
    end
end

-- Update function called every frame
function HailWeather.update(world, dt)
    -- Update day/night cycle based on world time
    local timeOfDay = world.timeOfDay or "day"
    local modifiers = HailWeather.timeModifiers[timeOfDay]
    
    if modifiers then
        -- Apply time-specific modifiers
        if modifiers.skyColor then
            -- Change sky color
        end
        
        if modifiers.ambientLightLevel then
            world.lightLevel = modifiers.ambientLightLevel
        end
    end
    
    -- Apply effects to game world if entity system exists
    if world.entitySystem and world.entitySystem.entities then
        for id, entity in pairs(world.entitySystem.entities) do
            -- Check if entity is exposed to hail
            if entity.isExposed then
                -- Apply combined damage
                if entity.health then
                    entity.health = entity.health - 
                        ((HailWeather.effects.exposureDamage + 
                          HailWeather.effects.freezingDamage) * dt)
                end
                
                -- Apply equipment damage
                if entity.equipment then
                    for _, item in pairs(entity.equipment) do
                        if item.durability then
                            item.durability = item.durability - 
                                (HailWeather.effects.equipmentDamageRate * dt)
                        end
                    end
                end
                
                -- Apply movement speed reduction
                if entity.speed then
                    entity.speed = entity.speed * HailWeather.effects.movementSpeedMultiplier
                end
                
                -- Apply ice build-up
                if entity.iceBuildUp then
                    entity.iceBuildUp = entity.iceBuildUp + 
                        (HailWeather.effects.iceBuildUpRate * dt)
                end
                
                -- Check for slipping
                if math.random() < HailWeather.effects.slipChance * dt then
                    if entity.onSlip then
                        entity:onSlip()
                    end
                end
            end
            
            -- Special effects for different entity types
            if entity.categories then
                -- Effect on mechanical devices
                if table.contains(entity.categories, "mechanical") then
                    if entity.iceBuildUp and entity.iceBuildUp > 0.8 then
                        if entity.onMalfunction then
                            entity:onMalfunction("ice_jam")
                        end
                    end
                end
                
                -- Effect on shields
                if table.contains(entity.categories, "shield") then
                    if entity.effectiveness then
                        entity.effectiveness = entity.effectiveness * 
                            HailWeather.effects.shieldEffectiveness
                    end
                end
                
                -- Effect on sensors and vision devices
                if table.contains(entity.categories, "sensor") then
                    if entity.range then
                        entity.range = entity.range * HailWeather.effects.visionRange
                    end
                end
            end
        end
    end
    
    -- Random hail impact sounds
    if math.random() < 0.1 then
        -- Play hail impact sound
        print("Playing sound: " .. HailWeather.sounds.impact)
    end
    
    -- Random ice break sounds
    if math.random() < 0.05 then
        -- Play ice break sound
        print("Playing sound: " .. HailWeather.sounds.iceBreak)
    end
end

-- Clean up when weather changes
function HailWeather.cleanUp(world)
    -- Stop ongoing sounds and particle effects
    print("Hail weather ending")
    
    -- Notify players if message system exists
    if world.messageSystem then
        world.messageSystem:broadcast("Hail storm passing. Conditions improving.")
    else
        print("Hail storm passing. Conditions improving.")
    end
    
    -- Reset entity states if entity system exists
    if world.entitySystem and world.entitySystem.entities then
        for id, entity in pairs(world.entitySystem.entities) do
            -- Reset movement speed
            if entity.speed and entity.defaultSpeed then
                entity.speed = entity.defaultSpeed
            end
            
            -- Reset shield effectiveness
            if entity.categories and table.contains(entity.categories, "shield") then
                if entity.effectiveness and entity.defaultEffectiveness then
                    entity.effectiveness = entity.defaultEffectiveness
                end
            end
            
            -- Reset sensor ranges
            if entity.categories and table.contains(entity.categories, "sensor") then
                if entity.range and entity.defaultRange then
                    entity.range = entity.defaultRange
                end
            end
            
            -- Clear ice build-up
            if entity.iceBuildUp then
                entity.iceBuildUp = 0
            end
        end
    end
end

-- Table contains helper function
function table.contains(table, element)
    for _, value in pairs(table) do
        if value == element then
            return true
        end
    end
    return false
end

return HailWeather 