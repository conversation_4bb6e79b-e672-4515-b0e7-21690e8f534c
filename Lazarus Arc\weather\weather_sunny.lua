-- weather/sunny.lua
-- Bright sunny weather pattern

local SunnyWeather = {}
SunnyWeather.__index = SunnyWeather

-- Basic properties
SunnyWeather.id = "sunny"
SunnyWeather.name = "Sunny"

-- Visual properties
SunnyWeather.visual = {
    skyColor = {r = 100, g = 180, b = 255}, -- Vivid blue
    sunIntensity = 1.4, -- Brighter than normal
    cloudCoverage = 0.0, -- No clouds
    sunGlare = 0.7, -- Strong sun glare effect
    ambientLightLevel = 1.2 -- Brighter than normal
}

-- Environment modifiers
SunnyWeather.environment = {
    temperature = 1.4, -- Hot
    visibility = 1.0, -- Maximum visibility
    humidity = 0.2, -- Dry
    windStrength = 0.1, -- Very slight breeze
    heatWave = false -- Can become a heat wave if prolonged
}

-- Particle systems
SunnyWeather.particles = {
    dust = {
        enabled = true,
        intensity = 0.15 -- Light dust in the air
    },
    heatDistortion = {
        enabled = true,
        intensity = 0.4 -- Heat shimmer effect
    }
}

-- Sound effects
SunnyWeather.sounds = {
    ambient = "ambient_hot_day",
    wildlife = "summer_birds",
    insects = "cicadas",
    volume = 0.6
}

-- Effects on game entities and tiles
SunnyWeather.effects = {
    -- Plants grow rapidly but may wither if too hot
    plantGrowthMultiplier = 1.5,
    plantWitherChance = 0.05, -- Small chance for plants to wither
    
    -- Animals seek shade
    animalShadeSeekingChance = 0.6,
    
    -- Tiles dry out quickly
    dryingRate = 0.3,
    
    -- Water evaporates faster
    waterEvaporationRate = 0.2,
    
    -- Players get hot
    playerTempEffect = 0.2, -- Per minute increase in body temp
    dehydrationRate = 0.15 -- Faster dehydration
}

-- Transition probabilities to other weather (per game hour)
SunnyWeather.transitions = {
    clear = 0.2,
    cloudy = 0.1,
    foggy = 0.02,
    rain = 0.05,
    snow = 0.0, -- Can't snow when it's this hot
    -- Implied: 0.63 chance to stay sunny
}

-- Heat wave state
SunnyWeather.heatWave = {
    active = false,
    duration = 0,
    maxDuration = 60, -- Game minutes
    temperatureBonus = 0.3,
    activationChance = 0.05 -- Per hour
}

-- Day/night cycle modifiers
SunnyWeather.timeModifiers = {
    dawn = {
        skyColor = {r = 255, g = 180, b = 130},
        ambientLightLevel = 0.8,
        sunGlare = 0.5
    },
    day = {
        -- Uses default properties
    },
    dusk = {
        skyColor = {r = 255, g = 160, b = 100},
        ambientLightLevel = 0.8,
        sunGlare = 0.6
    },
    night = {
        skyColor = {r = 20, g = 20, b = 50},
        ambientLightLevel = 0.15,
        sunIntensity = 0,
        sunGlare = 0,
        starVisibility = 0.9
    }
}

-- Helper function to deep copy a table
local function deepCopy(orig)
    local copy
    if type(orig) == 'table' then
        copy = {}
        for orig_key, orig_value in next, orig, nil do
            copy[deepCopy(orig_key)] = deepCopy(orig_value)
        end
        setmetatable(copy, deepCopy(getmetatable(orig)))
    else
        copy = orig
    end
    return copy
end

-- Create a new instance of the weather pattern
function SunnyWeather.new()
    local instance = setmetatable({}, SunnyWeather)
    -- Copy all properties
    for k, v in pairs(SunnyWeather) do
        if k ~= "__index" and k ~= "new" and type(v) ~= "function" then
            instance[k] = deepCopy(v)
        end
    end
    return instance
end

-- Initialize the weather pattern
function SunnyWeather:init(world)
    if not world then return self end
    
    print("Initializing sunny weather")
    
    -- Set global light level
    if self.visual and self.visual.ambientLightLevel then
        world.lightLevel = self.visual.ambientLightLevel
    end
    
    -- Add particles
    if self.particles and self.particles.dust and self.particles.dust.enabled then
        -- In a real implementation, this would create dust particles
        print("Creating dust particles with intensity: " .. self.particles.dust.intensity)
    end
    
    if self.particles and self.particles.heatDistortion and self.particles.heatDistortion.enabled then
        -- In a real implementation, this would create heat distortion
        print("Creating heat distortion with intensity: " .. self.particles.heatDistortion.intensity)
    end
    
    -- Start ambient sounds
    -- In a real implementation, this would play the sounds
    if self.sounds then
        print("Playing sounds: " .. self.sounds.ambient .. ", " .. 
                               self.sounds.wildlife .. ", " ..
                               self.sounds.insects)
    end
    
    return self
end

-- Update function called every frame
function SunnyWeather:update(world, dt)
    if not world then return end
    
    -- Update day/night cycle based on world time
    local timeOfDay = world.timeOfDay or "day"
    local modifiers = self.timeModifiers and self.timeModifiers[timeOfDay]
    
    if modifiers then
        -- Apply time-specific modifiers
        if modifiers.skyColor then
            -- In a real implementation, this would change the sky color
        end
        
        if modifiers.ambientLightLevel then
            world.lightLevel = modifiers.ambientLightLevel
        end
    end
    
    -- Check for heat wave
    if self.heatWave then
        if not self.heatWave.active then
            -- Random chance to start heat wave during the day
            if timeOfDay == "day" and math.random() < self.heatWave.activationChance * dt then
                self.heatWave.active = true
                self.heatWave.duration = 0
                
                if self.environment then
                    self.environment.heatWave = true
                    self.environment.temperature = self.environment.temperature + 
                                                  self.heatWave.temperatureBonus
                end
                
                print("A heat wave begins!")
            end
        else
            -- Update heat wave duration
            self.heatWave.duration = self.heatWave.duration + dt
            
            -- End heat wave if it has lasted long enough
            if self.heatWave.duration >= self.heatWave.maxDuration then
                self.heatWave.active = false
                
                if self.environment then
                    self.environment.heatWave = false
                    self.environment.temperature = self.environment.temperature - 
                                                  self.heatWave.temperatureBonus
                end
                
                print("The heat wave subsides.")
            end
        end
    end
    
    -- Apply effects to game world
    if world.entitySystem and world.entitySystem.entities then
        for id, entity in pairs(world.entitySystem.entities) do
            -- Affect plants
            if entity.categories and table.contains(entity.categories, "plant") and self.effects then
                -- Boost plant growth
                if entity.growthStage ~= nil then
                    entity.growthStage = entity.growthStage + 
                        (0.001 * self.effects.plantGrowthMultiplier * dt)
                    
                    -- But also chance to wither if it's very hot
                    if self.heatWave and self.heatWave.active and 
                       math.random() < self.effects.plantWitherChance * dt then
                        entity.growthStage = math.max(0, entity.growthStage - 0.05)
                        entity.withered = true
                    end
                end
            end
            
            -- Affect water bodies
            if (entity.type == "water" or 
               (entity.categories and table.contains(entity.categories, "water"))) and self.effects then
                -- Evaporate water
                if entity.waterLevel then
                    entity.waterLevel = entity.waterLevel - self.effects.waterEvaporationRate * dt
                    
                    -- If the water has dried up completely
                    if entity.waterLevel <= 0 then
                        -- Transform to dried up version
                        entity.type = "dried_mud"
                        print("A water body has dried up")
                    end
                end
            end
            
            -- Affect animals
            if entity.categories and table.contains(entity.categories, "animal") and self.effects then
                -- Animals seek shade
                if math.random() < self.effects.animalShadeSeekingChance * dt then
                    -- In a real implementation this would make the animal seek shaded tiles
                    if entity.wanderState and entity.wanderState.setNewTarget then
                        -- Prefer targets that are shaded
                        -- This is just a placeholder for the behavior
                    end
                end
            end
            
            -- Affect players
            if entity.type == "player" and self.effects then
                -- Increase body temperature
                if entity.stats and entity.stats.bodyTemperature then
                    entity.stats.bodyTemperature = entity.stats.bodyTemperature + 
                        (self.effects.playerTempEffect * dt)
                end
                
                -- Increase dehydration
                if entity.stats and entity.stats.hydration then
                    entity.stats.hydration = math.max(0, entity.stats.hydration - 
                        (self.effects.dehydrationRate * dt))
                end
            end
        end
    end
end

-- Clean up when weather changes
function SunnyWeather:cleanUp(world)
    if not world then return end
    
    -- Stop any ongoing sounds or particle effects
    print("Sunny weather ending")
    
    -- End heat wave if active
    if self.heatWave and self.heatWave.active then
        self.heatWave.active = false
        
        if self.environment then
            self.environment.heatWave = false
        end
    end
end

-- Table contains helper function
function table.contains(table, element)
    if not table then return false end
    
    for _, value in pairs(table) do
        if value == element then
            return true
        end
    end
    return false
end

return SunnyWeather
