-- weather/clear.lua
-- Clear weather pattern

local ClearWeather = {
    id = "clear",
    name = "Clear Sky",
    
    -- Visual properties
    visual = {
        skyColor = {r = 135, g = 206, b = 235}, -- Light blue
        sunIntensity = 1.0,
        cloudCoverage = 0.05,
        cloudColor = {r = 255, g = 255, b = 255},
        ambientLightLevel = 1.0
    },
    
    -- Environment modifiers
    environment = {
        temperature = 1.0, -- Neutral temperature multiplier
        visibility = 1.0, -- Maximum visibility
        humidity = 0.3, -- Below average humidity
        windStrength = 0.2 -- Light breeze
    },
    
    -- Particle systems
    particles = {
        dust = {
            enabled = true,
            intensity = 0.1 -- Very light dust
        }
    },
    
    -- Sound effects
    sounds = {
        ambient = "ambient_day",
        wind = "light_breeze",
        volume = 0.5
    },
    
    -- Effects on game entities and tiles
    effects = {
        -- Plants grow better in clear weather
        plantGrowthMultiplier = 1.2,
        
        -- Animals are more active
        animalActivityMultiplier = 1.3,
        
        -- Tiles dry out slowly
        dryingRate = 0.1
    },
    
    -- Transition probabilities to other weather (per game hour)
    transitions = {
        cloudy = 0.15,
        foggy = 0.05,
        rain = 0.05,
        snow = 0.01,
        sunny = 0.1,
        -- Implied: 0.64 chance to stay clear
    },
    
    -- Day/night cycle modifiers
    timeModifiers = {
        dawn = {
            skyColor = {r = 255, g = 200, b = 170},
            ambientLightLevel = 0.7
        },
        day = {
            -- Uses default properties
        },
        dusk = {
            skyColor = {r = 255, g = 180, b = 140},
            ambientLightLevel = 0.7
        },
        night = {
            skyColor = {r = 25, g = 25, b = 60},
            ambientLightLevel = 0.2,
            starVisibility = 1.0
        }
    }
}

-- Initialize the weather pattern
function ClearWeather.init(world)
    print("Initializing clear weather")
    
    -- Set global light level
    world.lightLevel = ClearWeather.visual.ambientLightLevel
    
    -- Start ambient sounds if any
    if ClearWeather.sounds.ambient then
        -- In a real implementation, this would play the sound
        print("Playing sound: " .. ClearWeather.sounds.ambient)
    end
    
    -- Add dust particles if needed
    if ClearWeather.particles.dust.enabled then
        -- In a real implementation, this would create dust particles
        print("Creating dust particles with intensity: " .. ClearWeather.particles.dust.intensity)
    end
    
    -- Notify players if messaging system exists
    if world.messageSystem then
        world.messageSystem:broadcast("Weather has cleared up.")
    end
end

-- Update function called every frame
function ClearWeather.update(world, dt)
    -- Update day/night cycle based on world time
    local timeOfDay = world.timeOfDay or "day"
    local modifiers = ClearWeather.timeModifiers[timeOfDay]
    
    if modifiers then
        -- Apply time-specific modifiers
        if modifiers.skyColor then
            -- In a real implementation, this would change the sky color
        end
        
        if modifiers.ambientLightLevel then
            world.lightLevel = modifiers.ambientLightLevel
        end
    end
    
    -- Apply slight random variations to make weather more dynamic
    if math.random() < 0.01 then
        ClearWeather.environment.windStrength = math.min(0.3, math.max(0.1, 
                                             ClearWeather.environment.windStrength + 
                                             (math.random() * 0.1 - 0.05)))
    end
    
    -- Apply effects to game world if entity system exists
    if world.entitySystem and world.entitySystem.entities then
        for id, entity in pairs(world.entitySystem.entities) do
            -- Affect plants
            if entity.categories and table.contains(entity.categories, "plant") then
                -- Boost plant growth
                if entity.growthStage ~= nil then
                    entity.growthStage = entity.growthStage + 
                        (0.001 * ClearWeather.effects.plantGrowthMultiplier * dt)
                end
            end
            
            -- Affect animals
            if entity.categories and table.contains(entity.categories, "animal") then
                -- More activity in clear weather
                if entity.wanderState and entity.wanderState.config then
                    entity.wanderState.config.idleChance = entity.wanderState.config.idleChance * 0.8
                end
            end
        end
    end
end

-- Clean up when weather changes
function ClearWeather.cleanUp(world)
    -- Stop any ongoing sounds or particle effects
    print("Clear weather ending")
end

-- Table contains helper function
function table.contains(table, element)
    for _, value in pairs(table) do
        if value == element then
            return true
        end
    end
    return false
end

return ClearWeather
