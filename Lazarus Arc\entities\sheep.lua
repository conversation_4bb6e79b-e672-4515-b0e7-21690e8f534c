-- entities/sheep.lua
local Sheep = {
    id = "sheep",
    name = "Sheep",
    type = "sheep",
    -- PLACEHOLDER: Basic shape for visual representation
    shape = {
        {0, -1}, {0.8, -0.8}, {1, 0}, {0.8, 0.8},
        {0, 1}, {-0.8, 0.8}, {-1, 0}, {-0.8, -0.8}
    },
    size = 8,

    -- Entity categories
    categories = {"animal", "livestock", "mammal", "medium"},
    
    -- Threat categories
    threatCategories = {"predator", "monster"},
    
    -- Stats
    maxHealth = 25,
    health = 25,
    maxStamina = 35,
    stamina = 35,
    speed = 1.8,
    
    -- Behaviors
    behaviors = {"graze", "wander", "flee"},
    
    -- Behavior configurations
    behaviorConfigs = {
        graze = {
            moveSpeed = 0.8,
            grazeRadius = 6,
            grazeTime = 8
        },
        wander = {
            moveSpeed = 1.0,
            changeDirectionChance = 0.03,
            idleChance = 0.5
        },
        flee = {
            moveSpeed = 2.5,
            detectionRadius = 10,
            panicDuration = 5
        }
    },
    
    -- Appearance configuration
    appearance = {
        sprite = "sheep",
        scale = 1.1,
        animations = {
            "idle", "walk", "run", "graze"
        },
        variants = {
            "white", "brown", "black"
        }
    },
    
    -- Drops
    drops = {
        {id = "wool", chance = 1.0, quantity = {1, 3}},
        {id = "meat", chance = 0.8, quantity = {1, 2}},
        {id = "hide", chance = 0.7, quantity = {1, 1}}
    },

    -- Sound effects (uses violin instrument for gentle, pastoral sounds)
    sounds = {
        bleat = "sheep_bleat",
        footstep = "sheep_footstep",
        hurt = "sheep_hurt",
        death = "sheep_death",
        idle = "sheep_idle",
        graze = "sheep_graze",
        alert = "sheep_alert"
    }
}

function Sheep.init(entity, world)
    -- Copy all fields from Sheep template to entity instance
    for k, v in pairs(Sheep) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random sheep variant (with safety checks)
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

return Sheep
