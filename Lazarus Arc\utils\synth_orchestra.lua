-- utils/synth_orchestra.lua
-- Multi-instrument synthesizer system for diverse entity sounds
-- Provides piano, drums, violin, brass, woodwinds, and more for rich audio variety

local SynthOrchestra = {
    initialized = false,
    sampleRate = 22050,
    baseFrequency = 440, -- A4
    
    -- Instrument definitions with unique characteristics
    instruments = {
        -- GRAND_PIANO - Rich, resonant concert piano
        grand_piano = {
            attack = 0.03, decay = 0.4, sustain = 0.7, release = 0.6,
            harmonics = {{1.0, 1.0}, {2.0, 0.6}, {3.0, 0.35}, {4.0, 0.2}, {5.0, 0.1}},
            waveform = "sine",
            resonant = true
        },

        -- UPRIGHT_PIANO - Classic, warm home piano
        upright_piano = {
            attack = 0.05, decay = 0.3, sustain = 0.6, release = 0.4,
            harmonics = {{1.0, 1.0}, {2.0, 0.5}, {3.0, 0.25}, {4.0, 0.125}},
            waveform = "sine"
        },

        -- ELECTRIC_PIANO - Vintage electric piano (Rhodes/Wu<PERSON>itzer style)
        electric_piano = {
            attack = 0.02, decay = 0.25, sustain = 0.5, release = 0.3,
            harmonics = {{1.0, 1.0}, {2.0, 0.4}, {3.0, 0.6}, {4.0, 0.2}},
            waveform = "sine",
            electric = true
        },

        -- ACOUSTIC_GUITAR - Warm, natural guitar
        acoustic_guitar = {
            attack = 0.01, decay = 0.3, sustain = 0.4, release = 0.5,
            harmonics = {{1.0, 1.0}, {2.0, 0.7}, {3.0, 0.5}, {4.0, 0.3}, {5.0, 0.2}},
            waveform = "triangle",
            stringResonance = true
        },

        -- ELECTRIC_GUITAR - Bright, sustained electric guitar
        electric_guitar = {
            attack = 0.02, decay = 0.2, sustain = 0.8, release = 0.4,
            harmonics = {{1.0, 1.0}, {2.0, 0.8}, {3.0, 0.6}, {4.0, 0.4}, {5.0, 0.3}},
            waveform = "sawtooth",
            electric = true,
            distortion = true
        },

        -- BASS_GUITAR - Deep, punchy bass guitar
        bass_guitar = {
            attack = 0.03, decay = 0.25, sustain = 0.7, release = 0.3,
            harmonics = {{1.0, 1.0}, {2.0, 0.4}, {3.0, 0.2}, {4.0, 0.1}},
            waveform = "square",
            electric = true
        },

        -- CLASSICAL_GUITAR - Nylon string, gentle
        classical_guitar = {
            attack = 0.01, decay = 0.35, sustain = 0.3, release = 0.6,
            harmonics = {{1.0, 1.0}, {2.0, 0.5}, {3.0, 0.3}, {4.0, 0.15}},
            waveform = "sine",
            nylonStrings = true
        },

        -- XYLOPHONE - Bright, percussive wooden mallet sounds
        xylophone = {
            attack = 0.01, decay = 0.2, sustain = 0.1, release = 0.3,
            harmonics = {{1.0, 1.0}, {3.0, 0.6}, {5.0, 0.4}, {7.0, 0.2}},
            waveform = "sine",
            metallic = true
        },

        -- TRUMPET - Bright, brassy, heroic tones
        trumpet = {
            attack = 0.08, decay = 0.15, sustain = 0.85, release = 0.25,
            harmonics = {{1.0, 1.0}, {2.0, 0.9}, {3.0, 0.7}, {4.0, 0.5}, {5.0, 0.3}},
            waveform = "square",
            naturalVibrato = true
        },

        -- CELLO - Deep, rich string tones
        cello = {
            attack = 0.15, decay = 0.1, sustain = 0.9, release = 0.4,
            harmonics = {{1.0, 1.0}, {2.0, 0.7}, {3.0, 0.5}, {4.0, 0.3}},
            waveform = "sawtooth",
            naturalVibrato = true
        },

        -- CLARINET - Smooth, woody wind instrument
        clarinet = {
            attack = 0.12, decay = 0.08, sustain = 0.8, release = 0.3,
            harmonics = {{1.0, 1.0}, {3.0, 0.4}, {5.0, 0.2}}, -- Odd harmonics
            waveform = "square",
            breathNoise = true
        },

        -- MARIMBA - Warm, wooden percussion
        marimba = {
            attack = 0.02, decay = 0.3, sustain = 0.2, release = 0.5,
            harmonics = {{1.0, 1.0}, {2.4, 0.5}, {4.2, 0.3}, {6.8, 0.15}},
            waveform = "sine"
        },

        -- SAXOPHONE - Smooth, jazzy reed instrument
        saxophone = {
            attack = 0.1, decay = 0.12, sustain = 0.8, release = 0.35,
            harmonics = {{1.0, 1.0}, {2.0, 0.6}, {3.0, 0.8}, {4.0, 0.4}},
            waveform = "sawtooth",
            breathNoise = true
        },

        -- BANJO - Twangy, plucked string instrument
        banjo = {
            attack = 0.005, decay = 0.25, sustain = 0.15, release = 0.4,
            harmonics = {{1.0, 1.0}, {2.0, 0.7}, {3.0, 0.5}, {4.0, 0.3}},
            waveform = "triangle",
            twang = true
        },

        -- ACCORDION - Reedy, folk instrument with vibrato
        accordion = {
            attack = 0.2, decay = 0.1, sustain = 0.9, release = 0.3,
            harmonics = {{1.0, 1.0}, {2.0, 0.5}, {3.0, 0.7}, {4.0, 0.3}},
            waveform = "square",
            naturalVibrato = true,
            breathNoise = true
        },

        -- VIBRAPHONE - Metallic, mellow mallet percussion
        vibraphone = {
            attack = 0.02, decay = 0.4, sustain = 0.6, release = 1.0,
            harmonics = {{1.0, 1.0}, {2.4, 0.4}, {3.8, 0.3}, {5.2, 0.2}},
            waveform = "sine",
            naturalVibrato = true,
            metallic = true
        },

        -- HARMONICA - Small, breathy reed instrument
        harmonica = {
            attack = 0.05, decay = 0.05, sustain = 0.8, release = 0.2,
            harmonics = {{1.0, 1.0}, {2.0, 0.4}, {3.0, 0.6}, {4.0, 0.2}},
            waveform = "square",
            breathNoise = true
        },

        -- DULCIMER - Plucked/hammered string instrument
        dulcimer = {
            attack = 0.01, decay = 0.35, sustain = 0.3, release = 0.6,
            harmonics = {{1.0, 1.0}, {2.0, 0.6}, {3.0, 0.4}, {4.0, 0.25}},
            waveform = "triangle"
        },

        -- OCARINA - Ceramic wind instrument, mystical
        ocarina = {
            attack = 0.08, decay = 0.06, sustain = 0.75, release = 0.25,
            harmonics = {{1.0, 1.0}, {2.0, 0.3}, {3.0, 0.15}},
            waveform = "sine",
            breathNoise = true
        },

        -- KALIMBA - Thumb piano, metallic plucked tines
        kalimba = {
            attack = 0.005, decay = 0.4, sustain = 0.1, release = 0.8,
            harmonics = {{1.0, 1.0}, {2.7, 0.5}, {4.3, 0.3}, {6.1, 0.2}},
            waveform = "sine",
            metallic = true
        },

        -- DIDGERIDOO - Deep, droning wind instrument
        didgeridoo = {
            attack = 0.3, decay = 0.1, sustain = 0.95, release = 0.4,
            harmonics = {{1.0, 1.0}, {2.0, 0.2}, {3.0, 0.1}},
            waveform = "square",
            breathNoise = true
        },

        -- STEEL_DRUM - Caribbean percussion, metallic
        steel_drum = {
            attack = 0.01, decay = 0.3, sustain = 0.4, release = 0.6,
            harmonics = {{1.0, 1.0}, {2.3, 0.7}, {3.7, 0.5}, {5.1, 0.3}},
            waveform = "sine",
            metallic = true
        }
    },
    
    -- Entity to instrument mappings
    entityInstruments = {
        -- Small creatures - delicate, high instruments
        rabbit = "kalimba",
        mouse = "harmonica",
        field_mouse = "harmonica",
        squirrel = "acoustic_guitar",
        butterfly = "vibraphone",
        fly = "xylophone",

        -- Birds - wind and melodic instruments
        bird = "ocarina",
        pigeon = "clarinet",
        songbird = "classical_guitar",
        crow = "electric_guitar",
        hawk = "trumpet",
        eagle = "trumpet",
        owl = "cello",
        duck = "dulcimer",
        goose = "saxophone",
        swan = "vibraphone",
        crane = "clarinet",
        flamingo = "accordion",
        pelican = "saxophone",

        -- Medium mammals - string and folk instruments
        sheep = "cello",
        goat = "acoustic_guitar",
        deer = "classical_guitar",
        horse = "grand_piano",

        -- Predators - powerful, deep instruments
        wolf = "bass_guitar",
        fox = "electric_guitar",
        bear = "bass_guitar",
        raccoon = "electric_piano",

        -- Aquatic - flowing, resonant instruments
        fish = "dulcimer",
        bass = "bass_guitar",
        trout = "classical_guitar",
        crab = "steel_drum",
        turtle = "upright_piano",
        frog = "kalimba",
        dolphin = "electric_piano",
        whale = "bass_guitar",
        shark = "electric_guitar",

        -- Magical/Boss - mystical instruments
        ancient_treant = "grand_piano",
        forest_golem = "bass_guitar",
        mystic_deer = "vibraphone",
        mimic = "electric_piano",

        -- Default fallbacks
        player = "grand_piano",
        npc = "upright_piano",
        default = "upright_piano"
    },
    
    -- Note frequencies cache
    noteFrequencies = {},
    
    -- Sound cache
    soundCache = {},
    
    -- Octave ranges
    octaveRanges = {
        tiny = 3,      -- Very small creatures
        small = 2,     -- Small creatures  
        medium = 1,    -- Medium creatures
        large = 0,     -- Large creatures
        huge = -1,     -- Huge creatures
        bass = -2      -- Massive creatures
    }
}

-- Initialize the synth orchestra system
function SynthOrchestra.init()
    if SynthOrchestra.initialized then return SynthOrchestra end
    
    print("Initializing Synth Orchestra system...")
    
    -- Generate note frequencies
    SynthOrchestra.generateNoteFrequencies()
    
    SynthOrchestra.initialized = true
    print("Synth Orchestra system initialized with " .. 
          SynthOrchestra.getInstrumentCount() .. " instruments")
    return SynthOrchestra
end

-- Generate note frequencies for 12-tone equal temperament
function SynthOrchestra.generateNoteFrequencies()
    local notes = {"C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"}
    
    for octave = -3, 4 do
        SynthOrchestra.noteFrequencies[octave] = {}
        for i, note in ipairs(notes) do
            local semitonesFromA4 = (octave * 12) + (i - 10)
            local frequency = SynthOrchestra.baseFrequency * math.pow(2, semitonesFromA4 / 12)
            SynthOrchestra.noteFrequencies[octave][note] = frequency
        end
    end
end

-- Get instrument count
function SynthOrchestra.getInstrumentCount()
    local count = 0
    for _ in pairs(SynthOrchestra.instruments) do
        count = count + 1
    end
    return count
end

-- Get appropriate instrument for entity type
function SynthOrchestra.getEntityInstrument(entityType)
    return SynthOrchestra.entityInstruments[entityType] or 
           SynthOrchestra.entityInstruments.default
end

-- Determine octave based on entity size
function SynthOrchestra.getOctaveForSize(entitySize)
    if entitySize <= 3 then
        return SynthOrchestra.octaveRanges.tiny
    elseif entitySize <= 6 then
        return SynthOrchestra.octaveRanges.small
    elseif entitySize <= 10 then
        return SynthOrchestra.octaveRanges.medium
    elseif entitySize <= 15 then
        return SynthOrchestra.octaveRanges.large
    elseif entitySize <= 20 then
        return SynthOrchestra.octaveRanges.huge
    else
        return SynthOrchestra.octaveRanges.bass
    end
end

-- Generate different waveforms
function SynthOrchestra.generateWaveform(waveform, frequency, t, options)
    options = options or {}

    if waveform == "sine" then
        return math.sin(2 * math.pi * frequency * t)
    elseif waveform == "square" then
        return math.sin(2 * math.pi * frequency * t) > 0 and 1 or -1
    elseif waveform == "sawtooth" then
        return 2 * (frequency * t - math.floor(frequency * t + 0.5))
    elseif waveform == "triangle" then
        local phase = frequency * t - math.floor(frequency * t)
        return phase < 0.5 and (4 * phase - 1) or (3 - 4 * phase)
    elseif waveform == "noise" then
        return (math.random() * 2 - 1) * 0.3 -- Scaled noise
    else
        return math.sin(2 * math.pi * frequency * t) -- Default to sine
    end
end

-- Generate a synthesized tone with specified instrument
function SynthOrchestra.generateTone(frequency, duration, instrumentName, options)
    if not SynthOrchestra.initialized then SynthOrchestra.init() end

    options = options or {}
    local instrument = SynthOrchestra.instruments[instrumentName] or SynthOrchestra.instruments.upright_piano

    -- Override instrument settings with options
    local attack = options.attack or instrument.attack
    local decay = options.decay or instrument.decay
    local sustain = options.sustain or instrument.sustain
    local release = options.release or instrument.release
    local volume = options.volume or 0.3
    local vibrato = options.vibrato or instrument.naturalVibrato or false
    local vibratoRate = options.vibratoRate or 5.0
    local vibratoDepth = options.vibratoDepth or 0.02

    -- Calculate sample count
    local sampleCount = math.floor(duration * SynthOrchestra.sampleRate)
    local samples = {}

    -- Generate samples
    for i = 0, sampleCount - 1 do
        local t = i / SynthOrchestra.sampleRate
        local sample = 0

        -- Apply vibrato if enabled
        local vibratoMod = 1.0
        if vibrato then
            vibratoMod = 1.0 + vibratoDepth * math.sin(2 * math.pi * vibratoRate * t)
        end

        local modFreq = frequency * vibratoMod

        -- Generate harmonics with instrument-specific waveform
        for _, harmonic in ipairs(instrument.harmonics) do
            local harmFreq = modFreq * harmonic[1]
            local harmAmp = harmonic[2]

            local waveValue = SynthOrchestra.generateWaveform(instrument.waveform, harmFreq, t, options)
            sample = sample + harmAmp * waveValue
        end

        -- Add special effects for certain instruments
        if instrument.breathNoise then
            sample = sample + (math.random() * 2 - 1) * 0.05 -- Subtle breath noise
        end

        if instrument.useNoise then
            sample = sample * 0.7 + (math.random() * 2 - 1) * 0.3 -- Mix with noise
        end

        if instrument.metallic then
            -- Add metallic shimmer with high frequency content
            sample = sample + 0.1 * math.sin(2 * math.pi * modFreq * 8 * t) * envelope
        end

        if instrument.twang then
            -- Add banjo-like twang effect
            local twangDecay = math.exp(-t * 15)
            sample = sample + 0.2 * math.sin(2 * math.pi * modFreq * 2.1 * t) * twangDecay
        end

        if instrument.electric then
            -- Add electric instrument characteristics
            sample = sample * (1 + 0.1 * math.sin(2 * math.pi * modFreq * 0.5 * t)) -- Slight modulation
        end

        if instrument.distortion then
            -- Add guitar distortion effect
            sample = math.tanh(sample * 2) * 0.7 -- Soft clipping distortion
        end

        if instrument.stringResonance then
            -- Add acoustic guitar string resonance
            sample = sample + 0.05 * math.sin(2 * math.pi * modFreq * 1.5 * t) * envelope
        end

        if instrument.nylonStrings then
            -- Add classical guitar nylon string warmth
            sample = sample * (1 - 0.1 * math.sin(2 * math.pi * modFreq * 3 * t) * envelope)
        end

        if instrument.resonant then
            -- Add grand piano resonance
            sample = sample + 0.08 * math.sin(2 * math.pi * modFreq * 0.25 * t) * envelope
        end

        -- Apply ADSR envelope
        local envelope = 1.0
        if t < attack then
            envelope = t / attack
        elseif t < attack + decay then
            local decayProgress = (t - attack) / decay
            envelope = 1.0 - (1.0 - sustain) * decayProgress
        elseif t < duration - release then
            envelope = sustain
        else
            local releaseProgress = (t - (duration - release)) / release
            envelope = sustain * (1.0 - releaseProgress)
        end

        -- Apply envelope and volume
        sample = sample * envelope * volume

        -- Clamp to prevent clipping
        sample = math.max(-1.0, math.min(1.0, sample))
        samples[i + 1] = sample
    end

    return samples
end

-- Create a LÖVE audio source from samples
function SynthOrchestra.createAudioSource(samples)
    if not love.audio or not love.sound then return nil end

    local soundData = love.sound.newSoundData(#samples, SynthOrchestra.sampleRate, 16, 1)

    for i = 1, #samples do
        soundData:setSample(i - 1, samples[i])
    end

    return love.audio.newSource(soundData)
end

-- Generate entity-appropriate sound
function SynthOrchestra.generateEntitySound(entityType, entitySize, soundName, options)
    if not SynthOrchestra.initialized then SynthOrchestra.init() end

    options = options or {}

    -- Get appropriate instrument for this entity
    local instrumentName = SynthOrchestra.getEntityInstrument(entityType)

    -- Determine octave based on entity size
    local octave = SynthOrchestra.getOctaveForSize(entitySize or 6)

    -- Determine note, duration based on sound type
    local note = "C"
    local duration = 0.5

    -- Sound type mappings with instrument-specific adjustments
    if soundName:find("footstep") or soundName:find("walk") or soundName:find("step") then
        note = "C"
        duration = 0.15
        options.volume = 0.2
        -- Use marimba for footsteps - warm, woody percussion
        instrumentName = "marimba"
    elseif soundName:find("attack") or soundName:find("bite") or soundName:find("claw") then
        note = "G"
        duration = 0.25
        options.volume = 0.4
        -- Use aggressive instruments for attacks
        if instrumentName == "kalimba" or instrumentName == "harmonica" or instrumentName == "dulcimer" then
            instrumentName = "electric_guitar"
        elseif instrumentName == "classical_guitar" or instrumentName == "acoustic_guitar" then
            instrumentName = "electric_guitar"
        end
    elseif soundName:find("hurt") or soundName:find("pain") or soundName:find("damage") then
        note = "D#"
        duration = 0.4
        options.vibrato = true
        options.vibratoRate = 8.0
        options.volume = 0.3
        -- Use expressive instruments for pain
        if instrumentName == "xylophone" or instrumentName == "steel_drum" then
            instrumentName = "cello"
        end
    elseif soundName:find("death") or soundName:find("die") then
        note = "F"
        duration = 1.2
        options.vibrato = true
        options.vibratoRate = 3.0
        options.volume = 0.4
        -- Use dramatic instruments for death
        if instrumentName == "marimba" or instrumentName == "kalimba" then
            instrumentName = "bass_guitar"
        elseif instrumentName == "upright_piano" or instrumentName == "electric_piano" then
            instrumentName = "grand_piano"
        end
    elseif soundName:find("call") or soundName:find("caw") or soundName:find("chirp") or soundName:find("song") then
        note = "A"
        duration = 0.8
        options.vibrato = true
        options.vibratoRate = 4.0
        options.volume = 0.35
        -- Keep natural instrument for calls/songs
    elseif soundName:find("growl") or soundName:find("roar") or soundName:find("snarl") then
        note = "E"
        duration = 0.6
        octave = octave - 1  -- Lower pitch for threatening sounds
        options.volume = 0.4
        -- Use deep, powerful instruments for growls
        instrumentName = "bass_guitar"
    elseif soundName:find("magic") or soundName:find("spell") or soundName:find("cast") then
        note = "B"
        duration = 0.5
        options.volume = 0.35
        instrumentName = "vibraphone" -- Mystical, shimmering for magic
    elseif soundName:find("splash") or soundName:find("swim") then
        note = "F#"
        duration = 0.3
        options.volume = 0.25
        instrumentName = "steel_drum" -- Metallic splash sounds
    else
        -- Default ambient/idle sound
        note = "A"
        duration = 0.5
        options.volume = 0.2
    end

    -- Ensure octave is in valid range
    octave = math.max(-3, math.min(4, octave))

    -- Get frequency for the note
    if not SynthOrchestra.noteFrequencies[octave] or not SynthOrchestra.noteFrequencies[octave][note] then
        print("Warning: Invalid note " .. note .. " in octave " .. octave)
        return nil
    end

    local frequency = SynthOrchestra.noteFrequencies[octave][note]

    -- Generate the tone
    local samples = SynthOrchestra.generateTone(frequency, duration, instrumentName, options)
    return SynthOrchestra.createAudioSource(samples)
end

-- Cache management
function SynthOrchestra.getCacheKey(entityType, entitySize, soundName, options)
    options = options or {}
    return string.format("%s_%d_%s_%.1f_%s",
        entityType or "default",
        entitySize or 6,
        soundName or "idle",
        options.volume or 0.3,
        SynthOrchestra.getEntityInstrument(entityType)
    )
end

function SynthOrchestra.getEntitySound(entityType, entitySize, soundName, options)
    local cacheKey = SynthOrchestra.getCacheKey(entityType, entitySize, soundName, options)

    if not SynthOrchestra.soundCache[cacheKey] then
        SynthOrchestra.soundCache[cacheKey] = SynthOrchestra.generateEntitySound(entityType, entitySize, soundName, options)
    end

    return SynthOrchestra.soundCache[cacheKey]
end

function SynthOrchestra.clearCache()
    SynthOrchestra.soundCache = {}
    print("Synth Orchestra cache cleared")
end

function SynthOrchestra.getCacheStats()
    local count = 0
    for _ in pairs(SynthOrchestra.soundCache) do
        count = count + 1
    end
    return {
        cachedSounds = count,
        instruments = SynthOrchestra.getInstrumentCount(),
        memoryEstimate = count * 0.7 .. " KB"
    }
end

-- Get list of all available instruments
function SynthOrchestra.getInstrumentList()
    local instruments = {}
    for name, _ in pairs(SynthOrchestra.instruments) do
        table.insert(instruments, name)
    end
    table.sort(instruments)
    return instruments
end

-- Get entity-to-instrument mapping summary
function SynthOrchestra.getEntityInstrumentSummary()
    local summary = {}
    for entityType, instrument in pairs(SynthOrchestra.entityInstruments) do
        if not summary[instrument] then
            summary[instrument] = {}
        end
        table.insert(summary[instrument], entityType)
    end
    return summary
end

return SynthOrchestra
