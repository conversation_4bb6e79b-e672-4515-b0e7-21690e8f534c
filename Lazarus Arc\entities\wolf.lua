-- entities/wolf.lua
local Wolf = {
    id = "wolf",
    name = "Wolf",
    type = "wolf",
    -- PLACEHOLDER: Basic shape for visual representation
    shape = {
        {0, -1}, {0.8, -0.8}, {1, 0}, {0.8, 0.8},
        {0, 1}, {-0.8, 0.8}, {-1, 0}, {-0.8, -0.8}
    },
    size = 9,

    -- Entity categories for relationship handling
    categories = {"animal", "predator", "mammal", "large"},

    -- Threat and food categories
    threatCategories = {"monster", "player"},
    foodCategories = {"medium_prey", "small_prey", "livestock"},

    -- Stats
    maxHealth = 45,
    health = 45,
    maxStamina = 60,
    stamina = 60,
    speed = 2.7,

    -- Behaviors
    behaviors = {"hunt", "pack", "wander"},

    -- Behavior configurations
    behaviorConfigs = {
        hunt = {
            moveSpeed = 3.0,
            huntRadius = 15,
            chaseRadius = 20,
            packBonus = 0.2
        },
        pack = {
            packRadius = 8,
            minPackSize = 2,
            maxPackSize = 5
        },
        wander = {
            moveSpeed = 1.8,
            changeDirectionChance = 0.04
        }
    },

    -- Special abilities
    abilities = {
        bite = {
            damageMultiplier = 1.2,
            cooldown = 3
        },
        howl = {
            range = 30, -- How far the howl travels
            effect = "summon_pack" -- Can summon other wolves
        }
    },

    -- Appearance
    appearance = {
        sprite = "wolf",
        scale = 1.2,
        animations = {
            "idle", "walk", "run", "attack", "howl"
        },
        variants = {
            "gray", "black", "white", "timber"
        }
    },

    -- Sound effects (uses bass instrument for deep, powerful sounds)
    sounds = {
        howl = "wolf_howl",
        bark = "wolf_bark",
        growl = "wolf_growl",
        footstep = "wolf_footstep",
        attack = "wolf_attack",
        hurt = "wolf_hurt",
        death = "wolf_death",
        idle = "wolf_idle",
        snarl = "wolf_snarl"
    },

    -- Loot drops
    drops = {
        {id = "meat", chance = 0.8, quantity = {1, 2}},
        {id = "hide", chance = 0.7, quantity = {1, 1}},
        {id = "fang", chance = 0.4, quantity = {1, 2}},
        {id = "claw", chance = 0.5, quantity = {2, 4}},
        {id = "bone", chance = 0.6, quantity = {1, 2}}
    }
}

-- Initialize the entity
function Wolf.init(entity, world)
    -- Copy all fields from Wolf template to entity instance
    for k, v in pairs(Wolf) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random wolf variant (with safety checks)
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

return Wolf