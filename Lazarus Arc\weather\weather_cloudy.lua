-- weather/cloudy.lua
-- Overcast cloudy weather pattern

local CloudyWeather = {
    id = "cloudy",
    name = "Cloudy",
    
    -- Visual properties
    visual = {
        skyColor = {r = 180, g = 190, b = 200}, -- Light gray-blue
        sunIntensity = 0.5, -- Sun dimmed by cloud cover
        cloudCoverage = 0.8, -- Heavy cloud cover
        cloudColor = {r = 220, g = 220, b = 220}, -- Light gray clouds
        cloudLayers = 3, -- Multiple cloud layers
        cloudMovementSpeed = 0.3,
        ambientLightLevel = 0.8 -- Slightly dimmer than clear
    },
    
    -- Environment modifiers
    environment = {
        temperature = 0.8, -- Slightly cooler
        visibility = 0.9, -- Good visibility
        humidity = 0.6, -- Moderate humidity
        windStrength = 0.4, -- Moderate breeze
        airPressure = 0.9 -- Slightly lower air pressure
    },
    
    -- Particle systems
    particles = {
        -- No specific particles for standard cloudy weather
    },
    
    -- Sound effects
    sounds = {
        ambient = "ambient_cloudy",
        wind = "soft_wind",
        volume = 0.4
    },
    
    -- Effects on game entities and tiles
    effects = {
        -- Plants grow normally
        plantGrowthMultiplier = 1.0,
        
        -- Animals behave normally
        animalActivityMultiplier = 1.0,
        
        -- Tiles dry more slowly
        dryingRate = 0.05
    },
    
    -- Transition probabilities to other weather (per game hour)
    transitions = {
        clear = 0.15,
        sunny = 0.1,
        foggy = 0.15,
        rain = 0.25, -- Higher chance to rain from cloudy
        snow = 0.1, -- Can snow if cold enough
        -- Implied: 0.25 chance to stay cloudy
    },
    
    -- Day/night cycle modifiers
    timeModifiers = {
        dawn = {
            skyColor = {r = 190, g = 180, b = 170},
            ambientLightLevel = 0.6,
            cloudColor = {r = 210, g = 180, b = 170} -- Pinkish clouds at dawn
        },
        day = {
            -- Uses default properties
        },
        dusk = {
            skyColor = {r = 170, g = 160, b = 170},
            ambientLightLevel = 0.6,
            cloudColor = {r = 200, g = 170, b = 180} -- Purplish clouds at dusk
        },
        night = {
            skyColor = {r = 40, g = 40, b = 60},
            ambientLightLevel = 0.15,
            sunIntensity = 0,
            cloudColor = {r = 60, g = 60, b = 70}, -- Dark gray clouds
            starVisibility = 0.1 -- Stars mostly hidden by clouds
        }
    },
    
    -- Cloud formations
    cloudFormations = {
        "stratus", -- Default for cloudy weather
        "stratocumulus",
        "altostratus"
    }
}

-- Initialize the weather pattern
function CloudyWeather.init(world)
    print("Initializing cloudy weather")
    
    -- Set global light level
    world.lightLevel = CloudyWeather.visual.ambientLightLevel
    
    -- Choose cloud formation
    local formation = CloudyWeather.cloudFormations[math.random(#CloudyWeather.cloudFormations)]
    CloudyWeather.currentFormation = formation
    print("Cloud formation: " .. formation)
    
    -- Start ambient sounds
    if CloudyWeather.sounds.ambient then
        -- In a real implementation, this would play the sound
        print("Playing sound: " .. CloudyWeather.sounds.ambient)
    end
    
    -- Initialize cloud movement direction
    CloudyWeather.cloudDirection = math.random() * math.pi * 2
    
    -- Notify players if messaging system exists
    if world.messageSystem then
        world.messageSystem:broadcast("Clouds have gathered in the sky.")
    end
end

-- Update function called every frame
function CloudyWeather.update(world, dt)
    -- Update day/night cycle based on world time
    local timeOfDay = world.timeOfDay or "day"
    local modifiers = CloudyWeather.timeModifiers[timeOfDay]
    
    if modifiers then
        -- Apply time-specific modifiers
        if modifiers.skyColor then
            -- In a real implementation, this would change the sky color
        end
        
        if modifiers.ambientLightLevel then
            world.lightLevel = modifiers.ambientLightLevel
        end
    end
    
    -- Update cloud movement
    CloudyWeather.cloudPosition = (CloudyWeather.cloudPosition or 0) + 
                                  CloudyWeather.visual.cloudMovementSpeed * dt
    
    -- Occasionally change wind direction and speed
    if math.random() < 0.01 then
        CloudyWeather.environment.windStrength = math.min(0.7, math.max(0.2, 
                                               CloudyWeather.environment.windStrength + 
                                               (math.random() * 0.2 - 0.1)))
        
        CloudyWeather.cloudDirection = CloudyWeather.cloudDirection + 
                                      (math.random() * 0.2 - 0.1)
    end
    
    -- Apply slight variation to cloud coverage
    if math.random() < 0.02 then
        CloudyWeather.visual.cloudCoverage = math.min(0.95, math.max(0.6, 
                                            CloudyWeather.visual.cloudCoverage + 
                                            (math.random() * 0.1 - 0.05)))
    end
    
    -- Chance to produce occasional light wind gusts
    if math.random() < 0.03 * dt then
        -- In a real implementation, this would create a brief wind effect
        print("Wind gust")
    end
    
    -- Apply effects to game world if entity system exists
    if world.entitySystem and world.entitySystem.entities then
        for id, entity in pairs(world.entitySystem.entities) do
            -- Weather prediction for some animals
            if entity.categories and (table.contains(entity.categories, "bird") or
                                     table.contains(entity.categories, "wildlife")) then
                
                -- Some animals can sense impending rain
                if math.random() < 0.2 * dt then
                    -- This would trigger specific behaviors for weather-sensitive animals
                    -- For example, birds might fly lower or return to nests
                    if entity.wanderState and entity.wanderState.config then
                        entity.wanderState.config.wanderRadius = entity.wanderState.config.wanderRadius * 0.8
                    end
                end
            end
            
            -- Affect plants less than sunny weather
            if entity.categories and table.contains(entity.categories, "plant") then
                if entity.growthStage ~= nil then
                    entity.growthStage = entity.growthStage + 
                        (0.0005 * CloudyWeather.effects.plantGrowthMultiplier * dt)
                end
            end
        end
    end
    
    -- Calculate chance of weather transition
    if world.weatherSystem and math.random() < 0.001 * dt then
        -- Increase chance of rain the longer it's cloudy
        CloudyWeather.transitions.rain = math.min(0.5, CloudyWeather.transitions.rain + 0.001)
    end
end

-- Clean up when weather changes
function CloudyWeather.cleanUp(world)
    -- Stop any ongoing sounds or particle effects
    print("Cloudy weather ending")
    
    -- Notify players if messaging system exists
    if world.messageSystem then
        world.messageSystem:broadcast("The clouds are beginning to clear.")
    end
end

-- Table contains helper function
function table.contains(table, element)
    for _, value in pairs(table) do
        if value == element then
            return true
        end
    end
    return false
end

return CloudyWeather
