local Eagle = {
    id = "eagle",
    name = "<PERSON>",
    type = "bird",
    shape = {
        {0, -1}, {0.7, -0.7}, {1, 0}, {0.7, 0.7},
        {0, 1}, {-0.7, 0.7}, {-1, 0}, {-0.7, -0.7}
    },
    size = 8,
    
    -- Entity categories
    categories = {"animal", "bird", "flying", "predator"},
    
    -- Threat and food categories
    threatCategories = {"player"},
    foodCategories = {"small_prey", "medium_prey", "fish"},
    
    -- Stats
    maxHealth = 45,
    health = 45,
    maxStamina = 80,
    stamina = 80,
    speed = 2.5,
    
    -- Flight properties
    flight = {
        maxHeight = 15,
        minHeight = 2,
        ascentSpeed = 1.2,
        descentSpeed = 2.0,
        hoverHeight = 5,
        currentHeight = 5,
        wingFlapRate = 0.1,
        soarChance = 0.7
    },
    
    -- Behaviors
    behaviors = {"hunt", "patrol", "roost", "territorial"},
    
    -- Behavior configurations
    behaviorConfigs = {
        hunt = {
            moveSpeed = 3.0,
            searchRadius = 30,
            diveSpeed = 4.0,
            attackRange = 2,
            damage = 25,
            preferredPrey = {"rabbit", "fish", "bird"}
        },
        patrol = {
            moveSpeed = 2.5,
            patrolRadius = 40,
            soarChance = 0.7,
            restInterval = {10, 20}
        },
        roost = {
            startTime = "dusk",
            endTime = "dawn",
            roostHeight = {4, 8},
            healthRegen = 0.05,
            staminaRegen = 0.1
        },
        territorial = {
            territoryRadius = 50,
            chaseRadius = 30,
            warningRadius = 20,
            aggressionLevel = 0.8
        }
    },
    
    -- Special abilities
    abilities = {
        dive = {
            speedBoost = 3.5,
            duration = 1.5,
            cooldown = 5,
            staminaCost = 20
        },
        talonGrip = {
            damage = 15,
            duration = 2,
            cooldown = 3,
            staminaCost = 10
        },
        screech = {
            range = 15,
            duration = 1,
            cooldown = 4,
            effect = "fear"
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "eagle",
        scale = 1.0,
        animations = {
            "idle", "fly", "dive", "attack", "screech"
        },
        variants = {
            "bald", "golden", "harpy", "imperial"
        }
    },
    
    -- Sound effects
    sounds = {
        screech = "eagle_screech",
        wingFlap = "eagle_wing_flap",
        call = "eagle_call"
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.7, quantity = {2, 3}},
        {id = "feather", chance = 0.8, quantity = {2, 4}},
        {id = "talon", chance = 0.4, quantity = {1, 2}},
        {id = "beak", chance = 0.3, quantity = {1, 1}},
        {id = "bone", chance = 0.5, quantity = {1, 2}}
    }
}

-- Initialize the entity
function Eagle.init(entity, world)
    -- Copy all fields from Eagle template to entity instance
    for k, v in pairs(Eagle) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random eagle variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

-- Update the entity
function Eagle.update(entity, world, dt)
    -- Update behaviors
    for _, behaviorName in ipairs(entity.behaviors) do
        local behavior = world.modules.behaviors[behaviorName]
        if behavior and behavior.update then
            behavior.update(entity, world, dt)
        end
    end

    -- Update flight properties
    if entity.flight then
        -- Update wing flapping based on height and activity
        if entity.flight.currentHeight > entity.flight.minHeight then
            if entity.flight.soarChance > math.random() then
                entity.flight.wingFlapRate = 0.05
            else
                entity.flight.wingFlapRate = 0.1
            end
        else
            entity.flight.wingFlapRate = 0.2
        end
    end
end

return Eagle 