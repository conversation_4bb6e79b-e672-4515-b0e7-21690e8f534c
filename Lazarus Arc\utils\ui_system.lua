-- ui_system.lua
-- UI system for Lazarus Arc

-- Helper function to count table elements (including non-numeric keys)
if not table.count then
    table.count = function(t)
        local count = 0
        for _ in pairs(t) do
            count = count + 1
        end
        return count
    end
end

-- Get Engine reference
local Engine = require("engine")
local lunajson = require("lunajson")
-- Require UTF-8 library for proper text handling with non-ASCII characters
-- This is needed for emoji icons and special characters in the UI
local utf8 = require("utf8")

-- Load debug systems
-- ⚠️  PRODUCTION SECURITY WARNING:
-- The following debug systems include cheat functionality (god mode, no-clip, etc.)
-- that MUST be disabled or removed entirely in production MMO builds to prevent
-- exploitation. Consider using conditional compilation or build flags.
local Profiler = require("utils.profiler")
local MemoryMonitor = require("utils.memory_monitor")
local AIDebugOverlay = require("utils.ai_debug_overlay")
local GodMode = require("utils.god_mode")  -- ⚠️  REMOVE IN PRODUCTION
local NoClipMode = require("utils.noclip_mode")  -- ⚠️  REMOVE IN PRODUCTION
local EventLogViewer = require("utils.event_log_viewer")
local PathfindingHeatmap = require("utils.pathfinding_heatmap")
local LightingDebug = require("utils.lighting_debug")
local DebugOverlays = require("utils.debug_overlays")
local DebugConsole = require("utils.debug_console")

-- UI System
local UISystem = {
    -- Core UI state
    screenWidth = 800,
    screenHeight = 600,
    activeMenu = "main", -- Current active menu state
    menuStack = {}, -- Stack for menu navigation
    debugMenuVisible = false, -- Debug menu visibility flag

    -- UI Components
    components = {
        buttons = {},
        labels = {},
        panels = {},
        forms = {}
    },

    -- Main Menu State
    mainMenu = {
        active = false,
        hoveredButton = nil,
        selectedButton = 1,
        buttons = {},
        animationTime = 0,
        backgroundParticles = nil,
        logoScale = 0,
        logoY = 0,
        buttonOpacity = 0,
        playerSlots = {},
        allLocal = {}
    },

    -- Character Creation State
    characterCreation = {
        active = false,
        currentTab = "basic", -- Current active tab
        tabs = {"basic", "class", "skills", "equipment"}, -- Available tabs
        data = {
            name = "Player1",
            class = 1,
            kit = 1,
            skills = {},
            gender = "male",
            selectedSkills = {} -- Track selected skills
        },
        -- Added preview data
        preview = {
            stats = {},
            equipment = {},
            skills = {},
            skillPoints = 3 -- Starting skill points
        },
        -- Tab buttons
        buttons = {}
    },

    -- Popup System
    popup = {
        active = false,
        type = nil,
        title = "",
        items = {},
        selectedItem = 1
    },

    -- Message system
    messages = {}
}

-- Initialize the UI system
function UISystem:init(settings)
    self.screenWidth = settings.screenWidth or 800
    self.screenHeight = settings.screenHeight or 600

    -- Initialize fonts
    self.font = love.graphics.newFont(14)
    self.largeFont = love.graphics.newFont(20)
    self.titleFont = love.graphics.newFont(32)

    -- Initialize main menu animation properties
    self.mainMenu.animationTime = 0
    self.mainMenu.logoScale = 0
    self.mainMenu.logoY = -100
    self.mainMenu.buttonOpacity = 0

    -- Create particle system for background
    local particleImg = love.graphics.newCanvas(4, 4)
    love.graphics.setCanvas(particleImg)
    love.graphics.clear()
    love.graphics.setColor(1, 1, 1, 1)
    love.graphics.circle("fill", 2, 2, 2)
    love.graphics.setCanvas()

    self.mainMenu.backgroundParticles = love.graphics.newParticleSystem(particleImg, 300)
    self.mainMenu.backgroundParticles:setParticleLifetime(3, 8)
    self.mainMenu.backgroundParticles:setEmissionRate(25)
    self.mainMenu.backgroundParticles:setSizeVariation(0.5)
    self.mainMenu.backgroundParticles:setLinearAcceleration(-20, -10, 20, 10)
    self.mainMenu.backgroundParticles:setColors(
        0.3, 0.6, 1.0, 0.5,   -- Light blue start
        0.5, 0.8, 1.0, 0.3,   -- Mid fade
        0.3, 0.6, 1.0, 0      -- Fade out
    )
    self.mainMenu.backgroundParticles:setPosition(self.screenWidth / 2, self.screenHeight + 50)
    self.mainMenu.backgroundParticles:setEmissionArea("normal", self.screenWidth / 2, 10)

    -- Initialize UI components
    self:initMainMenu()
    self:initCharacterCreation()

    -- Initialize debug systems
    Profiler.init()
    MemoryMonitor.init()
    AIDebugOverlay.init()
    GodMode.init()
    NoClipMode.init()
    EventLogViewer.init()
    PathfindingHeatmap.init()
    LightingDebug.init()
    DebugOverlays.init()
    DebugConsole.init()

    -- Show main menu by default
    self.mainMenuActive = true
    return self
end

-- Initialize main menu components
function UISystem:initMainMenu()
    local sw, sh = self.screenWidth, self.screenHeight

    -- Layout parameters for NFC slots and bottom buttons
    local slotW, slotH, slotSpacing = 150, 150, 20
    local btnW, btnH, btnSpacing = 150, 40, 20
    local margin = 20

    -- Compute positions
    local totalSlotsW = 4 * slotW + 3 * slotSpacing
    local startSlotsX = (sw - totalSlotsW) / 2
    -- Button row Y (uniform for all buttons)
    local btnY = sh - btnH - margin
    -- Slot row Y (above buttons)
    local slotY = btnY - slotH - margin

    -- Initialize player slots as NFC cards in a single bottom row
    self.mainMenu.playerSlots = {}
    for i = 1, 4 do
        self.mainMenu.playerSlots[i] = {
            empty = true,
            name = "Empty Slot",
            level = 0,
            class = "",
            x = startSlotsX + (i-1) * (slotW + slotSpacing),
            y = slotY,
            width = slotW,
            height = slotH,
            hovered = false,
            selected = false,
            nfcIcon = "📶"  -- NFC symbol on each card
        }
    end

    -- Bottom menu buttons aligned under each NFC card:
    self.mainMenu.buttons = {}
    for i, slot in ipairs(self.mainMenu.playerSlots) do
        -- Determine button type and action per slot index
        local name, action, icon, color, hoverColor
        if i == 1 then
            name = "Load"
            action = function()
                local sel = self.mainMenu.selectedSlot
                if sel and not self.mainMenu.playerSlots[sel].empty then
                    self:loadOrCreatePlayer(sel)
                else
                    self:addMessage("Select a character slot first.", 2)
                end
            end
            icon, color, hoverColor = "📥", {0.2,0.6,0.2}, {0.3,0.7,0.3}
        elseif i == 2 then
            name = "Options"
            action = function() Engine.stateManager:changeState("options") end
            icon, color, hoverColor = "⚙️", {0.2,0.5,0.8}, {0.3,0.6,0.9}
        elseif i == 3 then
            name = "Character Creator"
            action = function() self:startCharacterCreation() end
            icon, color, hoverColor = "👤", {0.2,0.5,0.8}, {0.3,0.6,0.9}
        else
            name = "Exit"
        action = function() love.event.quit() end
            icon, color, hoverColor = "🚪", {0.7,0.2,0.2}, {0.9,0.3,0.3}
        end
        -- Position button under slot center using btnY
        local bx = slot.x + (slot.width - btnW) / 2
        local by = btnY
        self.mainMenu.buttons[i] = {
            text = name,
            x = bx, y = by,
            width = btnW, height = btnH,
            hovered = false,
            action = action,
            icon = icon,
            color = color,
            hoverColor = hoverColor,
            textColor = {1,1,1,1},
            animation = { offset = (i-1)*0.2, speed = 1 }
        }
    end
    self.mainMenu.selectedButton = nil
    self.mainMenu.hoveredButton = nil
    self.mainMenu.selectedSlot = nil

    -- Load character slots from absolute JSON files
    self:loadPlayerData()
end

--[[
    Player Loading and Registration System
    -------------------------------------

    This function is responsible for loading a character from a slot and creating
    a player entity that can be controlled in the game. It's a critical part of
    the character movement system because it:

    1. Creates the player entity with a unique ID
    2. Registers the player with the world's entity system
    3. Sets the player in the world using setPlayer
    4. Adds the player to the viewport manager for camera tracking

    The proper registration of the player entity is essential for character movement
    to work correctly. If any of these steps fail, the player may not move properly
    or may not be visible in the game world.

    The registration process creates a "single source of truth" for the player entity,
    ensuring that all systems (input, physics, rendering) reference the same player object.
]]

-- Load or create a player based on slot
function UISystem:loadOrCreatePlayer(slotIndex)
    local slot = self.mainMenu.playerSlots[slotIndex]

    if slot.empty then
        -- Slot is empty, go to character creation
        self.characterCreation.slotToFill = slotIndex
        self:startCharacterCreation()
    else
        -- Slot has a character: use the already loaded data
        local characterData = slot.character
        -- If not present, try JSON or database fallback (optional)
        if not characterData and Engine.systems.databaseManager then
            local saved = Engine.systems.databaseManager.getAllCharacters()
            characterData = saved and saved[slotIndex]
        end

        if characterData then
            -- STEP 1: Create the player entity
            -- This creates a new player entity with a unique ID (generated in Player.new)
            local Player = require("entities.player")
            local player = Player.new(characterData.name, characterData.class)

            -- Copy character data to player
            player.character = characterData

            -- Set player position to a valid starting point
            player.position = {x = 0, y = 0}

            -- Store player in Engine for global access
            Engine.player = player

            -- Ensure player has a unique ID (fallback, should be set in Player.new)
            if not player.id then
                player.id = "player1"
                print("WARNING: Player had no ID, assigned default 'player1'")
            end

            -- STEP 2: Register player with world entity system
            -- This is critical for the player to be recognized by the world
            if Engine.currentWorld and Engine.currentWorld.entitySystem then
                local success = Engine.currentWorld.entitySystem:register(player)
                DebugConsole.logPlayerRegistration(success, player.id)

                -- STEP 3: Set player in world for easy access
                -- This creates a "single source of truth" for the player entity
                Engine.currentWorld:setPlayer(player)
            else
                DebugConsole.logPlayerRegistration(false, "No world entity system")
            end

            -- Change game state to gameplay
            local sm = Engine.stateManager or Engine.stateHandlers
            if sm and sm.changeState then
                sm:changeState("gameplay")
            end

            if Engine.startSession then Engine.startSession() end
            self.mainMenuActive = false

            -- STEP 4: Add player to viewport manager for camera tracking
            -- This ensures the camera follows the player correctly
            if Engine.systems.viewportManager then
                Engine.systems.viewportManager:addPlayer({
                    id = player.id,
                    position = player.position,
                    character = player.character
                })
                print("Player added to viewport manager for camera tracking")
            else
                print("WARNING: No viewport manager available for player tracking")
            end

            self:addMessage("Welcome, " .. player.name .. "!", 3)
        else
            self:addMessage("Failed to load character", 3)
        end
    end
end

-- Initialize character creation components
function UISystem:initCharacterCreation()
    local centerX = self.screenWidth / 2
    local centerY = self.screenHeight / 2

    -- Make the character creation form much larger to avoid cramped UI
    -- Using almost the full screen for better readability and interaction
    local formWidth = self.screenWidth * 0.9  -- 90% of screen width
    local formHeight = self.screenHeight * 0.85  -- 85% of screen height
    local formX = centerX - formWidth/2
    local formY = centerY - formHeight/2 + 20  -- Slight offset from center

    -- Define appearance options
    self.characterCreation.appearanceOptions = {
        hair = {
            styles = {"short", "medium", "long", "curly", "spiky", "bald", "shonen", "fireforce", "sailormoon", "cowboybebop", "tenchi", "afro", "dreadlocks", "braids", "fade"},
            colors = {"black", "brown", "blonde", "red", "gray", "white", "blue", "green", "purple", "orange", "lightblue", "fieryred"}
        },
        skin = {
            tones = {"fair", "medium", "tan", "dark", "pale", "olive", "ebony", "golden", "caramel", "amber", "bronze", "copper", "mahogany", "espresso", "deepBrown"}
        },
        eyes = {
            styles = {"standard", "large", "sharp", "shonen", "fireforce", "sailormoon", "cowboybebop", "tenchi"},
            colors = {"blue", "green", "brown", "gray", "amber", "purple", "hazel", "red", "sharingan", "saiyan", "glowing"}
        },
        body = {
            types = {"thin", "average", "muscular", "heavy", "athletic", "shonen"}
        }
    }

        -- Character creation form
        self.components.forms.characterCreation = {
            x = formX,
            y = formY,
            width = formWidth,
            height = formHeight,
            fields = {
                -- Basic tab fields - larger and more spaced out
                name = {
                    x = formX + 80,
                    y = formY + 150,
                    width = 500,
                    height = 50,
                    label = "Character Name",
                    value = "Player", -- Default placeholder
                    tab = "basic",
                    editable = true, -- Mark as editable text field
    onInput = function(field, text)
        field.value = text
        self.characterCreation.data.name = text
    end
                },
            gender = {
                x = formX + 80,
                y = formY + 220,
                width = 500,
                height = 50,
                label = "Gender",
                value = "male",
                options = {"male", "female"},
                tab = "basic"
            },
            -- Class tab fields - larger and more spaced out
            class = {
                x = formX + 80,
                y = formY + 150,
                width = 500,
                height = 50,
                label = "Class",
                value = 1,
                options = Engine.systems.characterCreator.getAvailableClasses(),
                tab = "class"
            },
            -- Equipment tab fields - larger and more spaced out
            kit = {
                x = formX + 80,
                y = formY + 150,
                width = 500,
                height = 50,
                label = "Starting Kit",
                value = 1,
                options = {},
                tab = "equipment"
            }
        },
        buttons = {
            create = {
                x = centerX - 125,
                y = formY + formHeight - 80,
                width = 250,
                height = 60,
                text = "Create Character",
                color = {0.2, 0.6, 0.2},
                hoverColor = {0.3, 0.7, 0.3},
                textColor = {1, 1, 1},
                action = function() self:createCharacter() end,
                hovered = false
            },
            back = {
                x = centerX - 125,
                y = formY + formHeight - 150,
                width = 250,
                height = 60,
                text = "Back to Main Menu",
                color = {0.6, 0.2, 0.2},
                hoverColor = {0.7, 0.3, 0.3},
                textColor = {1, 1, 1},
                action = function()
                    print("Back button action triggered")
                    self:handleBackButtonClick()
                end,
                hovered = false,
                onClick = function()
                    print("Back button onClick triggered")
                    self:handleBackButtonClick()
                    return true
                end
            }
        }
    }

    -- Initialize kit options
    local kits = {}
    for kitName, _ in pairs(Engine.systems.characterCreator.startingKits) do
        table.insert(kits, kitName)
    end
    self.components.forms.characterCreation.fields.kit.options = kits

    -- Initialize tab buttons - making them larger and more spaced out
    local tabWidth = 160
    local tabHeight = 45
    local tabSpacing = 20
    local tabsStartX = formX + 40
    local tabsY = formY + 70

    for i, tabName in ipairs(self.characterCreation.tabs) do
        self.characterCreation.buttons[tabName] = {
            text = tabName:sub(1,1):upper() .. tabName:sub(2),
            x = tabsStartX + (i-1) * (tabWidth + tabSpacing),
            y = tabsY,
            width = tabWidth,
            height = tabHeight,
            hovered = false,
            color = {0.2, 0.3, 0.4},
            hoverColor = {0.3, 0.4, 0.5},
            textColor = {1, 1, 1},
            action = function() self.characterCreation.currentTab = tabName end
        }
    end
end

-- Create character from form data
function UISystem:createCharacter()
    local form = self.components.forms.characterCreation
    local data = self.characterCreation.data

    -- Validate name
    if not data.name or data.name == "" then
        self:addMessage("Please enter a character name.", 2)
        return
    end

    -- Create character object
    local character = {
        name = data.name,
        class = data.class,
        kit = data.kit,
        skills = data.skills,
        gender = data.gender,
        selectedSkills = data.selectedSkills
    }

    -- Save to slot
    local slot = self.mainMenu.playerSlots[self.characterCreation.slotToFill]
    slot.empty = false
    slot.character = character
    slot.name = character.name
    slot.class = character.class

    -- Return to main menu
    self.characterCreation.active = false
    self.mainMenuActive = true
end

-- Cancel character creation
function UISystem:cancelCharacterCreation()
    self.characterCreation.active = false
    self.mainMenuActive = true
    self:handleTextInputFocus()
end

-- Draw the debug menu
function UISystem:drawDebugMenu()
    if not self.debugMenuVisible then return end

    -- Semi-transparent background
    love.graphics.setColor(0, 0, 0, 0.7)
    love.graphics.rectangle("fill", 0, 0, self.screenWidth, self.screenHeight)

    -- Title
    love.graphics.setFont(self.titleFont)
    love.graphics.setColor(1, 1, 1, 1)
    love.graphics.print("Debug Menu", 50, 50)

    -- Draw debug options
    love.graphics.setFont(self.font)
    local startX1, startX2 = 50, 350
    local y1, y2 = 100, 100
    local lh = 28
    self.debugOptionButtons = {}

    local toggle = function(label, val, id, col)
        local x = (col == 2) and startX2 or startX1
        local y = (col == 2) and y2 or y1
        love.graphics.print(label .. ": " .. (val and "ON" or "OFF"), x, y)
        local bx, by, bw, bh = x + 180, y, 50, 20
        love.graphics.setColor(0.3,0.3,0.3)
        love.graphics.rectangle("fill", bx, by, bw, bh)
        love.graphics.setColor(1,1,1)
        love.graphics.print(val and "ON" or "OFF", bx+10, by+2)
        self.debugOptionButtons[id] = {x=bx, y=by, w=bw, h=bh}
        if col == 2 then y2 = y2 + lh else y1 = y1 + lh end
    end

    -- Column 1: Rendering & Basic
    toggle("Isometric View", Engine.systems.colorUtils and Engine.systems.colorUtils.isometricDebug, "iso", 1)
    toggle("Chunk Boundaries", Engine.settings.showChunkBoundaries or false, "chunk", 1)
    toggle("Collision Boxes", Engine.settings.showCollisionBoxes or false, "collision", 1)
    toggle("Render Entities", Engine.settings.renderEntities, "entities", 1)
    toggle("Render Tiles", Engine.settings.renderTiles, "tiles", 1)
    toggle("Wireframe Mode", Engine.settings.showWireframe or false, "wireframe", 1)
    toggle("Show Weather", Engine.settings.showWeather, "weather", 1)
    toggle("Show Decorations", Engine.settings.showDecorations, "decorations", 1)

    -- Column 2: Performance & Tools
    toggle("FPS Counter", Engine.settings.showFPS, "fps", 2)
    toggle("Profiler Graph", Engine.settings.showProfiler or false, "profiler", 2)
    toggle("Memory Usage", Engine.settings.showMemory or false, "memory", 2)
    toggle("Weather Debug", Engine.systems.weatherSystem and Engine.systems.weatherSystem.debugMode, "weatherDebug", 2)
    toggle("Deadzone/Grid", Engine.debug or Engine.debugMode, "deadzone", 2)
    toggle("AI State Overlay", Engine.settings.showAIStates or false, "aiState", 2)
    toggle("Pathfinding Heatmap", Engine.settings.showPathfinding or false, "pathfinding", 2)
    toggle("Lighting Debug", Engine.settings.showNormals or false, "lighting", 2)
    toggle("God Mode", Engine.settings.godMode or false, "god", 2)
    toggle("No-Clip", Engine.settings.noClip or false, "noclip", 2)
    toggle("Event-Log Viewer", Engine.settings.showEventLog or false, "eventLog", 2)
    toggle("Debug Console", DebugConsole.enabled or false, "debugConsole", 2)
    toggle("Tile Debug Mode", Engine.settings.debugTiles or false, "tileDebug", 2)

    -- Entity Spawning Section
    local spawnY = math.max(y1, y2) + 30
    love.graphics.setFont(self.largeFont)
    love.graphics.setColor(1, 1, 1, 1)
    love.graphics.print("Entity Spawning", 50, spawnY)
    spawnY = spawnY + 40
    love.graphics.setFont(self.font)

    -- Initialize entity lists if not already done
    if not self.debugEntityLists then
        self.debugEntityLists = {
            passive = {"deer", "rabbit", "crow", "goat", "songbird"},
            enemy = {"wolf", "bear", "bandit", "skeleton", "robot_guard"},
            boss = {"ancient_treant", "forest_guardian", "robot_overlord", "necromancer"}
        }
        self.debugSelectedEntities = {
            passive = "deer",
            enemy = "wolf",
            boss = "ancient_treant"
        }
    end

    -- Draw dropdown selectors and spawn buttons
    local btnW, btnH = 120, 30
    local dropW, dropH = 150, 30
    local spacing = 20
    local categories = {"passive", "enemy", "boss"}

    for i, category in ipairs(categories) do
        local y = spawnY + (i-1) * (btnH + spacing)

        -- Category label
        love.graphics.setColor(0.8, 0.8, 0.8, 1)
        love.graphics.print(category:sub(1,1):upper() .. category:sub(2) .. " Entity:", 50, y + 5)

        -- Dropdown button
        love.graphics.setColor(0.3, 0.3, 0.3, 1)
        local dropX = 200
        love.graphics.rectangle("fill", dropX, y, dropW, dropH, 5, 5)
        love.graphics.setColor(1, 1, 1, 1)
        love.graphics.print(self.debugSelectedEntities[category], dropX + 10, y + 5)

        -- Dropdown arrow
        love.graphics.setColor(0.8, 0.8, 0.8, 1)
        love.graphics.polygon("fill",
            dropX + dropW - 20, y + 10,
            dropX + dropW - 10, y + 10,
            dropX + dropW - 15, y + 20)

        -- Store dropdown hitbox
        self.debugOptionButtons["dropdown_" .. category] = {x = dropX, y = y, w = dropW, h = dropH}

        -- Spawn button
        local spawnX = dropX + dropW + spacing
        love.graphics.setColor(0.2, 0.5, 0.2, 1)
        love.graphics.rectangle("fill", spawnX, y, btnW, dropH, 5, 5)
        love.graphics.setColor(1, 1, 1, 1)
        love.graphics.print("Spawn", spawnX + 10, y + 5)

        -- Store spawn button hitbox
        self.debugOptionButtons["spawn_" .. category] = {x = spawnX, y = y, w = btnW, h = dropH}

        -- Draw dropdown list if open
        if self.debugOpenDropdown == category then
            local listY = y + dropH
            local listH = #self.debugEntityLists[category] * 25

            -- Dropdown background
            love.graphics.setColor(0.2, 0.2, 0.2, 0.95)
            love.graphics.rectangle("fill", dropX, listY, dropW, listH, 5, 5)

            -- Dropdown items
            for j, entity in ipairs(self.debugEntityLists[category]) do
                local itemY = listY + (j-1) * 25

                -- Highlight selected item
                if entity == self.debugSelectedEntities[category] then
                    love.graphics.setColor(0.3, 0.5, 0.7, 0.7)
                    love.graphics.rectangle("fill", dropX, itemY, dropW, 25)
                end

                love.graphics.setColor(1, 1, 1, 1)
                love.graphics.print(entity, dropX + 10, itemY + 5)

                -- Store item hitbox
                self.debugOptionButtons["dropdown_" .. category .. "_" .. entity] = {
                    x = dropX, y = itemY, w = dropW, h = 25
                }
            end
        end
    end

    -- Tile System Section
    local tileY = math.max(y1, y2) + 200
    love.graphics.setFont(self.largeFont)
    love.graphics.setColor(1, 1, 1, 1)
    love.graphics.print("Tile Rendering System", 50, tileY)
    tileY = tileY + 40
    love.graphics.setFont(self.font)

    -- Get current tile system info
    local tileInfo = nil
    if Engine.systems.renderer and Engine.systems.renderer.getTileSystemInfo then
        tileInfo = Engine.systems.renderer:getTileSystemInfo()
    end

    if tileInfo then
        -- Display current system
        love.graphics.setColor(0.8, 0.8, 0.8, 1)
        love.graphics.print("Current System: " .. tileInfo.activeSystem, 50, tileY)
        tileY = tileY + 25

        -- Display system description
        if tileInfo.systemConfig then
            love.graphics.setColor(0.6, 0.6, 0.6, 1)
            love.graphics.print("Description: " .. tileInfo.systemConfig.description, 50, tileY)
            tileY = tileY + 20
            love.graphics.print("Performance: " .. tileInfo.systemConfig.performance .. " | Quality: " .. tileInfo.systemConfig.quality, 50, tileY)
            tileY = tileY + 30
        end

        -- Switch system button
        love.graphics.setColor(0.2, 0.4, 0.6, 1)
        local switchBtnW, switchBtnH = 150, 30
        love.graphics.rectangle("fill", 50, tileY, switchBtnW, switchBtnH, 5, 5)
        love.graphics.setColor(1, 1, 1, 1)
        love.graphics.print("Switch Tile System", 60, tileY + 5)

        -- Store button hitbox
        self.debugOptionButtons["switchTileSystem"] = {x = 50, y = tileY, w = switchBtnW, h = switchBtnH}

        tileY = tileY + 40

        -- Available systems list
        love.graphics.setColor(0.7, 0.7, 0.7, 1)
        love.graphics.print("Available Systems:", 50, tileY)
        tileY = tileY + 20

        for _, system in ipairs(tileInfo.availableSystems) do
            local prefix = (system == tileInfo.activeSystem) and "→ " or "  "
            love.graphics.setColor(system == tileInfo.activeSystem and {0.4, 0.8, 0.4, 1} or {0.6, 0.6, 0.6, 1})
            love.graphics.print(prefix .. system, 70, tileY)
            tileY = tileY + 18
        end
    else
        love.graphics.setColor(0.8, 0.4, 0.4, 1)
        love.graphics.print("Tile system information not available", 50, tileY)
    end

    -- Instructions
    love.graphics.setFont(self.font)
    love.graphics.setColor(0.7, 0.7, 0.7)
    love.graphics.print("Press F1 to hide/show this menu", 50, self.screenHeight - 30)
end

-- Draw the UI
function UISystem:draw()
    -- Start profiler frame
    Profiler.startFrame()

    love.graphics.setColor(1, 1, 1)

    if self.inGameMenu and self.inGameMenu.active then
        self:drawInGameMenu()
    elseif self.characterCreation.active then
        self:drawCharacterCreation()

        -- Debug UI elements in character creation mode if debug is enabled
        if Engine.debug or Engine.debugMode then
            -- Draw debug outlines for all buttons
            love.graphics.setColor(1, 0, 0, 0.3)

            -- Draw outlines for tab buttons
            for _, tabName in ipairs(self.characterCreation.tabs) do
                local button = self.characterCreation.buttons[tabName]
                if button then
                    love.graphics.rectangle("line", button.x, button.y, button.width, button.height)
                    -- Draw button name for debugging
                    love.graphics.setColor(1, 1, 0, 0.8)
                    love.graphics.print(tabName, button.x, button.y - 15)
                end
            end

            -- Draw outlines for form buttons
            local form = self.components.forms.characterCreation
            if form and form.buttons then
                for buttonName, btn in pairs(form.buttons) do
                    love.graphics.setColor(0, 1, 0, 0.3)
                    love.graphics.rectangle("line", btn.x, btn.y, btn.width, btn.height)
                    -- Draw button name for debugging
                    love.graphics.setColor(1, 1, 0, 0.8)
                    love.graphics.print(buttonName, btn.x, btn.y - 15)
                end
            end

            -- Reset color
            love.graphics.setColor(1, 1, 1, 1)
        end
    elseif self.mainMenuActive then
        self:drawMainMenu()
    end

    -- Draw debug menu on top if visible
    if self.debugMenuVisible then
        self:drawDebugMenu()
    end

    if self.popup.active then
        self:drawPopup()
    end

    -- Draw messages
    self:drawMessages()

    -- Draw debug systems (after everything else so they appear on top)
    Profiler.draw()
    MemoryMonitor.draw()
    EventLogViewer.draw()

    -- Draw debug overlays that need camera info
    if Engine.currentWorld and Engine.currentWorld.camera then
        AIDebugOverlay.draw(Engine.currentWorld.entities, Engine.currentWorld.camera)
        PathfindingHeatmap.draw(Engine.currentWorld.camera)
        LightingDebug.draw(Engine.currentWorld.camera)

        -- Draw enhanced debug overlays
        DebugOverlays.drawChunkBoundaries(Engine.currentWorld, Engine.currentWorld.camera)
        DebugOverlays.drawCollisionBoxes(Engine.currentWorld, Engine.currentWorld.entities)
        DebugOverlays.drawWireframe(Engine.currentWorld, Engine.currentWorld.entities)
        DebugOverlays.drawDeadzone(Engine.player, Engine.currentWorld.camera)
    end

    -- Draw FPS counter (doesn't need camera)
    DebugOverlays.drawFPS()

    -- Draw debug console
    DebugConsole.draw()

    -- Draw god mode and no-clip indicators
    GodMode.drawIndicator(10, love.graphics.getHeight() - 60)
    NoClipMode.drawIndicator(10, love.graphics.getHeight() - 100)

    -- End profiler frame
    Profiler.endFrame()
end

-- Update UI animations and states
function UISystem:update(dt)
    -- Update main menu animations
    if not self.characterCreation.active then
        -- Update background particles
        if self.mainMenu.backgroundParticles then
            self.mainMenu.backgroundParticles:update(dt)
        end

        -- Update animation time
        self.mainMenu.animationTime = self.mainMenu.animationTime + dt

        -- Animate logo appearance
        if self.mainMenu.logoScale < 1 then
            self.mainMenu.logoScale = math.min(1, self.mainMenu.logoScale + dt * 2)
        end

        if self.mainMenu.logoY < 120 then
            self.mainMenu.logoY = math.min(120, self.mainMenu.logoY + dt * 200)
        end

        -- Animate button appearance
        if self.mainMenu.buttonOpacity < 1 then
            self.mainMenu.buttonOpacity = math.min(1, self.mainMenu.buttonOpacity + dt)
        end

        -- Animate buttons (disabled; fixed positioning)
        -- old animation removed to keep buttons on the same row
    end

    -- Update messages
    if self.messages then
        for i = #self.messages, 1, -1 do
            local message = self.messages[i]
            message.current = message.current + dt

            if message.current >= message.duration then
                table.remove(self.messages, i)
            end
        end
    end

    -- Update debug systems
    MemoryMonitor.update(dt)
    DebugConsole.update(dt)
    if Engine.currentWorld then
        PathfindingHeatmap.update(dt, Engine.currentWorld)
        LightingDebug.update(Engine.currentWorld, Engine.currentWorld.entities)
    end

    -- Update god mode and no-clip for player
    if Engine.player then
        GodMode.updatePlayer(Engine.player, dt)
        NoClipMode.updatePlayer(Engine.player, dt)
    end
end

-- Draw the main menu
function UISystem:drawMainMenu()
    -- Central area reserved for hub biome background (game view)
    -- Draw only UI overlay: NFC cards row and bottom buttons
    -- Draw player slots (NFC cards)
    for _, slot in ipairs(self.mainMenu.playerSlots) do
        -- Simple hover indicator
        local shadowAlpha = slot.hovered and 0.6 or 0.5
        -- Draw card shadow
        love.graphics.setColor(0, 0, 0, shadowAlpha)
        love.graphics.rectangle("fill", slot.x + 4, slot.y + 4, slot.width, slot.height, 6, 6)
        -- Card background
        local bg = slot.empty and {0.2, 0.2, 0.2, 0.8} or {0.1, 0.3, 0.6, 0.9}
        love.graphics.setColor(bg)
        love.graphics.rectangle("fill", slot.x, slot.y, slot.width, slot.height, 6, 6)
        -- Outline
        love.graphics.setColor(1, 1, 1, 0.7)
        love.graphics.rectangle("line", slot.x, slot.y, slot.width, slot.height, 6, 6)
        -- Content
        if slot.empty then
            love.graphics.setFont(self.font)
            love.graphics.setColor(0.8, 0.8, 0.8, 1)
            love.graphics.printf("Empty Slot", slot.x, slot.y + slot.height / 2 - 10, slot.width, "center")
        else
    love.graphics.setFont(self.largeFont)
            love.graphics.setColor(1, 1, 1, 1)
            love.graphics.printf(slot.name, slot.x, slot.y + 10, slot.width, "center")
            love.graphics.setFont(self.font)
            love.graphics.setColor(0.9, 0.9, 0.9, 1)
            love.graphics.printf("Lv " .. slot.level, slot.x, slot.y + slot.height - 20, slot.width, "center")
            -- NFC icon
            love.graphics.setFont(self.largeFont)
            love.graphics.setColor(1, 1, 1, 0.8)
            love.graphics.print(slot.nfcIcon, slot.x + slot.width - 24, slot.y + 4)
        end
        -- Selection highlight
        if slot.selected then
            love.graphics.setColor(0.4, 0.8, 1, 0.7)
            love.graphics.rectangle("line", slot.x - 2, slot.y - 2, slot.width + 4, slot.height + 4, 8, 8)
        end
    end
    -- Draw bottom menu buttons
    for _, btn in ipairs(self.mainMenu.buttons) do
        self:drawButton(btn)
    end
end

-- Draw messages
function UISystem:drawMessages()
    if not self.messages then return end

    love.graphics.setFont(self.font)

    local y = 50
    for i, message in ipairs(self.messages) do
        local alpha = 1

        -- Fade in/out effect
        if message.current < 0.5 then
            alpha = message.current * 2
        elseif message.current > message.duration - 0.5 then
            alpha = (message.duration - message.current) * 2
        end

        -- Draw message background
        love.graphics.setColor(0, 0, 0, 0.6 * alpha)
        local msgWidth = self.font:getWidth(message.text) + 20
        love.graphics.rectangle("fill", self.screenWidth - msgWidth - 10, y - 5, msgWidth, 30, 5, 5)

        -- Draw message text
        love.graphics.setColor(1, 1, 1, alpha)
        love.graphics.print(message.text, self.screenWidth - self.font:getWidth(message.text) - 20, y)

        y = y + 35
    end
end

-- Toggle debug menu
function UISystem:toggleDebugMenu()
    self.debugMenuVisible = not self.debugMenuVisible

    -- If we're closing the debug menu and the character screen is open, close it too
    if not self.debugMenuVisible and self.inGameMenu and self.inGameMenu.active then
        self:closeCharacterScreen()
        local Engine = require("engine")
        Engine.playerMenuVisible = false
        Engine.gamePaused = false
    end

    -- Add a message
    self:addMessage("Debug menu " .. (self.debugMenuVisible and "shown" or "hidden"), 2)

    return self.debugMenuVisible
end

-- Handle key press events in main menu
function UISystem:keyPressed(key)
    -- Handle F1 key to toggle debug menu
    if key == "f1" then
        self:toggleDebugMenu()
        return true
    end

    -- NOTE: No debug hotkeys in production! Debug systems should only be accessible
    -- through the debug menu (F1) to prevent players from accessing cheat functionality
    -- in the MMO environment. All debug features will be disabled in release builds.

    -- Let event log viewer handle keys
    if EventLogViewer.keyPressed(key) then
        return true
    end

    -- Let no-clip handle keys
    if NoClipMode.keyPressed(key) then
        return true
    end

    -- Handle escape key to return to main menu from character creation
    if key == "escape" and self.characterCreation.active then
        print("Escape key pressed in character creation - returning to main menu")
        self:handleBackButtonClick()
        return true
    end

    -- Handle text editing in character creation
    if self.characterCreation.active then
        -- Only process if we have an active text field
        if self.characterCreation.activeTextField == "name" then
            local form = self.components.forms and self.components.forms.characterCreation
            if form and form.fields and form.fields.name then
                local nf = form.fields.name

                -- Store cursor position if not already set
                if not self.characterCreation.cursorPos then
                    self.characterCreation.cursorPos = utf8.len(nf.value or "")
                end

                if key == "backspace" then
                    -- Delete character before cursor
                    if nf.value and #nf.value > 0 and self.characterCreation.cursorPos > 0 then
                        -- Get the byte position of the character before the cursor
                        local beforeCursor = utf8.offset(nf.value, self.characterCreation.cursorPos)
                        local prevCharPos = utf8.offset(nf.value, -1, beforeCursor)

                        if prevCharPos then
                            -- Remove the character before the cursor
                            nf.value = string.sub(nf.value, 1, prevCharPos - 1) ..
                                      string.sub(nf.value, beforeCursor)
                            self.characterCreation.data.name = nf.value
                            -- Move cursor back
                            self.characterCreation.cursorPos = self.characterCreation.cursorPos - 1
                        end
                    end
                    return true
                elseif key == "delete" then
                    -- Delete character after cursor
                    if nf.value and #nf.value > 0 and self.characterCreation.cursorPos < utf8.len(nf.value) then
                        -- Get the byte position of the cursor and the next character
                        local cursorPos = utf8.offset(nf.value, self.characterCreation.cursorPos + 1)
                        local nextCharPos = utf8.offset(nf.value, 2, cursorPos - 1)

                        if cursorPos and nextCharPos then
                            -- Remove the character at the cursor
                            nf.value = string.sub(nf.value, 1, cursorPos - 1) ..
                                      string.sub(nf.value, nextCharPos)
                            self.characterCreation.data.name = nf.value
                        elseif cursorPos then
                            -- If there's no next character, just truncate
                            nf.value = string.sub(nf.value, 1, cursorPos - 1)
                            self.characterCreation.data.name = nf.value
                        end
                    end
                    return true
                elseif key == "left" then
                    -- Move cursor left
                    if self.characterCreation.cursorPos > 0 then
                        self.characterCreation.cursorPos = self.characterCreation.cursorPos - 1
                    end
                    return true
                elseif key == "right" then
                    -- Move cursor right
                    if nf.value and self.characterCreation.cursorPos < utf8.len(nf.value) then
                        self.characterCreation.cursorPos = self.characterCreation.cursorPos + 1
                    end
                    return true
                elseif key == "home" then
                    -- Move cursor to start
                    self.characterCreation.cursorPos = 0
                    return true
                elseif key == "end" then
                    -- Move cursor to end
                    if nf.value then
                        self.characterCreation.cursorPos = utf8.len(nf.value)
                    else
                        self.characterCreation.cursorPos = 0
                    end
                    return true
                elseif key == "return" or key == "escape" then
                    -- Clear focus on Enter or Escape
                    self.characterCreation.activeTextField = nil
                    self.characterCreation.cursorPos = nil
                    -- Disable text input mode
                    love.keyboard.setTextInput(false)
                    return true
                end
            end
        elseif key == "return" or key == "escape" then
            -- Clear focus on Enter or Escape (when no text field is active)
            self.characterCreation.activeTextField = nil
            self.characterCreation.cursorPos = nil
            -- Disable text input mode
            love.keyboard.setTextInput(false)
            return true
        end
    end
    -- Check if in-game menu is active
    if self.inGameMenu and self.inGameMenu.active then
        if key == "escape" then
            self:closeCharacterScreen()
            return true
        elseif key == "tab" then
            -- Cycle through tabs
            local currentTabIndex = 1
            for i, tab in ipairs(self.inGameMenu.tabs) do
                if tab == self.inGameMenu.currentTab then
                    currentTabIndex = i
                    break
                end
            end

            -- Move to next tab
            currentTabIndex = currentTabIndex % #self.inGameMenu.tabs + 1
            self.inGameMenu.currentTab = self.inGameMenu.tabs[currentTabIndex]
            return true
        end
        return false
    end

    -- Handle Escape to open in-game menu when playing
    if key == "escape" and not self.characterCreation.active and not self.mainMenuActive and Engine.player then
        self:openCharacterScreen(Engine.player)
        return true
    end

    -- Handle main menu navigation
    if not self.characterCreation.active and self.mainMenuActive then
        if key == "up" then
            self.mainMenu.selectedButton = math.max(1, self.mainMenu.selectedButton - 1)
        elseif key == "down" then
            self.mainMenu.selectedButton = math.min(#self.mainMenu.buttons, self.mainMenu.selectedButton + 1)
        elseif key == "return" or key == "space" then
            -- Trigger selected button action
            local button = self.mainMenu.buttons[self.mainMenu.selectedButton]
            if button and button.action then
                button.action()
            end
        end
    end
end

-- Handle mouse movement for button hover states
function UISystem:mouseMove(x, y)
    -- First check if in-game menu is active
    if self.inGameMenu and self.inGameMenu.active then
        self:handleInGameMenuMouseMove(x, y)
        return
    end

    -- Check if character creation is active
    if self.characterCreation.active then
        -- Handle character creation tab button hover states
        for _, tabName in ipairs(self.characterCreation.tabs) do
            local button = self.characterCreation.buttons[tabName]
            if button then
                local wasHovered = button.hovered
                button.hovered = x >= button.x and x <= button.x + button.width and
                                 y >= button.y and y <= button.y + button.height

                -- Debug hover state changes
                if wasHovered ~= button.hovered and (Engine.debug or Engine.debugMode) then
                    print(string.format("Tab button '%s' hover state changed to: %s",
                        tabName, button.hovered and "hovered" or "not hovered"))
                end
            end
        end

        -- Handle form buttons hover states
        local form = self.components.forms and self.components.forms.characterCreation
        if form and form.buttons then
            for buttonName, btn in pairs(form.buttons) do
                local wasHovered = btn.hovered
                btn.hovered = x >= btn.x and x <= btn.x + btn.width and
                              y >= btn.y and y <= btn.y + btn.height

                -- Debug hover state changes
                if wasHovered ~= btn.hovered and (Engine.debug or Engine.debugMode) then
                    print(string.format("Form button '%s' hover state changed to: %s",
                        buttonName, btn.hovered and "hovered" or "not hovered"))
                end
            end
        end
        return
    end

    if self.mainMenuActive then
        self.mainMenu.hoveredButton = nil

        -- Check player slots hover states
        for i, slot in ipairs(self.mainMenu.playerSlots) do
            slot.hovered = x >= slot.x and x <= slot.x + slot.width and
                           y >= slot.y and y <= slot.y + slot.height

            -- Check play button hover state if slot is not empty
            if not slot.empty then
                local playBtnX = slot.x + slot.width - 100
                local playBtnY = slot.y + slot.height - 50
                local playBtnWidth = 80
                local playBtnHeight = 30

                slot.playButtonHovered = x >= playBtnX and x <= playBtnX + playBtnWidth and
                                        y >= playBtnY and y <= playBtnY + playBtnHeight
            end
        end

        -- Check main menu buttons hover states
        for i, button in ipairs(self.mainMenu.buttons) do
            button.hovered = false

            -- Check if mouse is hovering over button
            if x >= button.x and x <= button.x + button.width and
               y >= button.y and y <= button.y + button.height then
                self.mainMenu.hoveredButton = i
                button.hovered = true
                self.mainMenu.selectedButton = i
            end
        end
    end
end

-- Handle debug menu click
function UISystem:handleDebugMenuClick(x, y)
    if not self.debugMenuVisible then return false end

    -- Check if debug buttons are clicked
    if self.debugOptionButtons then
        local b = self.debugOptionButtons
        for id, btn in pairs(b) do
            if x >= btn.x and x <= btn.x + btn.w and y >= btn.y and y <= btn.y + btn.h then
                -- Handle dropdown toggles
                if id:find("dropdown_") == 1 then
                    -- Handle dropdown clicks
                    local parts = {}
                    for part in id:gmatch("[^_]+") do
                        table.insert(parts, part)
                    end

                    if #parts == 2 then
                        -- This is a dropdown toggle button
                        local category = parts[2]
                        if self.debugOpenDropdown == category then
                            self.debugOpenDropdown = nil
                        else
                            self.debugOpenDropdown = category
                        end
                        return true
                    elseif #parts == 3 then
                        -- This is a dropdown item selection
                        local category = parts[2]
                        local entity = parts[3]
                        self.debugSelectedEntities[category] = entity
                        self.debugOpenDropdown = nil
                        return true
                    end
                end

                -- Handle spawn buttons
                if id:find("spawn_") == 1 then
                    local category = id:sub(7)
                    local entityType = self.debugSelectedEntities[category]

                    -- Get player position or use center of screen
                    local x, y
                    if Engine.player and Engine.player.position then
                        x, y = Engine.player.position.x, Engine.player.position.y
                    else
                        -- Use a default position if player position is not available
                        x, y = 0, 0
                    end

                    -- Spawn the entity
                    if Engine.entitySystem and Engine.entitySystem.addEntity then
                        local entity = Engine.entitySystem:addEntity(entityType, x, y)
                        if entity then
                            self:addMessage("Spawned " .. entityType .. " at player position", 2)
                        else
                            self:addMessage("Failed to spawn " .. entityType, 2)
                        end
                    else
                        self:addMessage("Entity system not available", 2)
                    end
                    return true
                end

                -- Toggle buttons
                if id == "iso" then
                    local cu = Engine.systems.colorUtils
                    if cu then
                        cu.isometricDebug = not cu.isometricDebug
                        self:addMessage("Isometric View " .. (cu.isometricDebug and "Enabled" or "Disabled"), 2)
                    end
                    return true
                elseif id == "chunk" then
                    Engine.settings.showChunkBoundaries = not (Engine.settings.showChunkBoundaries or false)
                    DebugOverlays.chunkBoundaries.enabled = Engine.settings.showChunkBoundaries
                    self:addMessage("Chunk Boundaries " .. (Engine.settings.showChunkBoundaries and "Enabled" or "Disabled"), 2)
                    return true
                elseif id == "collision" then
                    Engine.settings.showCollisionBoxes = not (Engine.settings.showCollisionBoxes or false)
                    DebugOverlays.collisionBoxes.enabled = Engine.settings.showCollisionBoxes
                    self:addMessage("Collision Boxes " .. (Engine.settings.showCollisionBoxes and "Enabled" or "Disabled"), 2)
                    return true
                elseif id == "entities" then
                    Engine.settings.renderEntities = not Engine.settings.renderEntities
                    self:addMessage("Render Entities " .. (Engine.settings.renderEntities and "Enabled" or "Disabled"), 2)
                    return true
                elseif id == "tiles" then
                    Engine.settings.renderTiles = not Engine.settings.renderTiles
                    self:addMessage("Render Tiles " .. (Engine.settings.renderTiles and "Enabled" or "Disabled"), 2)
                    return true
                elseif id == "wireframe" then
                    Engine.settings.showWireframe = not (Engine.settings.showWireframe or false)
                    DebugOverlays.wireframe.enabled = Engine.settings.showWireframe
                    self:addMessage("Wireframe Mode " .. (Engine.settings.showWireframe and "Enabled" or "Disabled"), 2)
                    return true
                elseif id == "weather" then
                    Engine.settings.showWeather = not Engine.settings.showWeather
                    self:addMessage("Show Weather " .. (Engine.settings.showWeather and "Enabled" or "Disabled"), 2)
                    return true
                elseif id == "decorations" then
                    Engine.settings.showDecorations = not Engine.settings.showDecorations
                    self:addMessage("Show Decorations " .. (Engine.settings.showDecorations and "Enabled" or "Disabled"), 2)
                    return true
                elseif id == "fps" then
                    Engine.settings.showFPS = not Engine.settings.showFPS
                    DebugOverlays.fps.enabled = Engine.settings.showFPS
                    self:addMessage("FPS Counter " .. (Engine.settings.showFPS and "Enabled" or "Disabled"), 2)
                    return true
                elseif id == "profiler" then
                    Engine.settings.showProfiler = not (Engine.settings.showProfiler or false)
                    Profiler.enabled = Engine.settings.showProfiler
                    if Engine.settings.showProfiler then
                        Profiler.toggle()
                    end
                    self:addMessage("Profiler Graph " .. (Engine.settings.showProfiler and "Enabled" or "Disabled"), 2)
                    return true
                elseif id == "memory" then
                    Engine.settings.showMemory = not (Engine.settings.showMemory or false)
                    MemoryMonitor.enabled = Engine.settings.showMemory
                    if Engine.settings.showMemory then
                        MemoryMonitor.toggle()
                    end
                    self:addMessage("Memory Usage " .. (Engine.settings.showMemory and "Enabled" or "Disabled"), 2)
                    return true
                elseif id == "weatherDebug" then
                    local ws = Engine.systems.weatherSystem
                    if ws then
                        ws.debugMode = not ws.debugMode
                        self:addMessage("Weather Debug " .. (ws.debugMode and "Enabled" or "Disabled"), 2)
                    end
                    return true
                elseif id == "deadzone" then
                    Engine.debug = not Engine.debug
                    Engine.debugMode = Engine.debug
                    DebugOverlays.deadzone.enabled = Engine.debug
                    self:addMessage("Deadzone/Grid " .. (Engine.debug and "Enabled" or "Disabled"), 2)
                    return true
                elseif id == "aiState" then
                    Engine.settings.showAIStates = not (Engine.settings.showAIStates or false)
                    AIDebugOverlay.enabled = Engine.settings.showAIStates
                    if Engine.settings.showAIStates then
                        AIDebugOverlay.toggle()
                    end
                    self:addMessage("AI State Overlay " .. (Engine.settings.showAIStates and "Enabled" or "Disabled"), 2)
                    return true
                elseif id == "pathfinding" then
                    Engine.settings.showPathfinding = not (Engine.settings.showPathfinding or false)
                    PathfindingHeatmap.enabled = Engine.settings.showPathfinding
                    if Engine.settings.showPathfinding then
                        PathfindingHeatmap.toggle()
                    end
                    self:addMessage("Pathfinding Heatmap " .. (Engine.settings.showPathfinding and "Enabled" or "Disabled"), 2)
                    return true
                elseif id == "lighting" then
                    Engine.settings.showNormals = not (Engine.settings.showNormals or false)
                    LightingDebug.enabled = Engine.settings.showNormals
                    if Engine.settings.showNormals then
                        LightingDebug.toggle()
                    end
                    self:addMessage("Lighting Debug " .. (Engine.settings.showNormals and "Enabled" or "Disabled"), 2)
                    return true
                elseif id == "god" then
                    Engine.settings.godMode = not (Engine.settings.godMode or false)
                    GodMode.enabled = Engine.settings.godMode
                    if Engine.settings.godMode then
                        GodMode.toggle()
                    end
                    self:addMessage("God Mode " .. (Engine.settings.godMode and "Enabled" or "Disabled"), 2)
                    return true
                elseif id == "noclip" then
                    Engine.settings.noClip = not (Engine.settings.noClip or false)
                    NoClipMode.enabled = Engine.settings.noClip
                    if Engine.settings.noClip then
                        NoClipMode.toggle()
                    end
                    self:addMessage("No-Clip " .. (Engine.settings.noClip and "Enabled" or "Disabled"), 2)
                    return true
                elseif id == "eventLog" then
                    Engine.settings.showEventLog = not (Engine.settings.showEventLog or false)
                    EventLogViewer.enabled = Engine.settings.showEventLog
                    if Engine.settings.showEventLog then
                        EventLogViewer.toggle()
                    end
                    self:addMessage("Event-Log Viewer " .. (Engine.settings.showEventLog and "Enabled" or "Disabled"), 2)
                    return true
                elseif id == "debugConsole" then
                    DebugConsole.toggle()
                    self:addMessage("Debug Console " .. (DebugConsole.enabled and "Enabled" or "Disabled"), 2)
                    return true
                elseif id == "tileDebug" then
                    Engine.settings.debugTiles = not (Engine.settings.debugTiles or false)
                    if Engine.systems.renderer and Engine.systems.renderer.setTileDebugMode then
                        Engine.systems.renderer:setTileDebugMode(Engine.settings.debugTiles)
                    end
                    self:addMessage("Tile Debug Mode " .. (Engine.settings.debugTiles and "Enabled" or "Disabled"), 2)
                    return true
                elseif id == "switchTileSystem" then
                    -- Switch to next tile system
                    if Engine.systems.renderer and Engine.systems.renderer.switchTileSystem then
                        local success = Engine.systems.renderer:switchTileSystem()
                        if success then
                            local info = Engine.systems.renderer:getTileSystemInfo()
                            if info then
                                self:addMessage("Switched to tile system: " .. info.activeSystem, 3)
                            else
                                self:addMessage("Tile system switched", 2)
                            end
                        else
                            self:addMessage("Failed to switch tile system", 2)
                        end
                    else
                        self:addMessage("Tile system switching not available", 2)
                    end
                    return true
                end
            end
        end
    end

    -- Close any open dropdowns when clicking elsewhere
    self.debugOpenDropdown = nil

    return true -- Click was handled by debug menu
end

-- Handle main menu click
function UISystem:handleMainMenuClick(x, y)
    -- If debug menu is active, handle its clicks first
    if self.debugMenuVisible then
        return self:handleDebugMenuClick(x, y)
    end

    -- If a popup is active, handle its clicks first
    if self.popup.active and self.popup.type == "selectCharacter" then
        -- Assignment popup click: assign chosen JSON to the remembered slot
        for idx, _ in ipairs(self.popup.items) do
            -- Compute Y-range for each item
            local startY = (self.screenHeight/2 - 80) + (idx-1)*30
            if x >= (self.screenWidth/2-200) and x <= (self.screenWidth/2+200) and
               y >= startY and y <= (startY+20) then
                local slotIndex = self.mainMenu.popupSlotIndex
                if slotIndex and self.mainMenu.allLocal[idx] then
                    local character = self.mainMenu.allLocal[idx]
                    local slot = self.mainMenu.playerSlots[slotIndex]
                    -- Assign to this slot
                    slot.empty = false
                    slot.source = "json"
                    slot.jsonIndex = idx
                    slot.name = character.name or slot.name
                    slot.level = character.level or slot.level
                    slot.class = character.class or slot.class
                    slot.character = character
                    -- Remove duplicates elsewhere
                    local idKey = character.id or character.uuid
                    if idKey then
                        for j, other in ipairs(self.mainMenu.playerSlots) do
                            if j ~= slotIndex and other.character then
                                local otherId = other.character.id or other.character.uuid
                                if otherId == idKey then
                                    other.empty = true
                                    other.source = nil
                                    other.character = nil
                                    other.name = "Empty Slot"
                                    other.level = 0
                                    other.class = ""
                                end
                            end
                        end
                    end
                end
                self:hidePopup()
                self.mainMenu.popupSlotIndex = nil
                return true
            end
        end
        return true
    end
    if self.inGameMenu and self.inGameMenu.active then
        return self:handleInGameMenuClick(x, y)
    end

    if not self.characterCreation.active and self.mainMenuActive then
        -- Check player slot clicks: simply select a slot
        for i, slot in ipairs(self.mainMenu.playerSlots) do
            if x >= slot.x and x <= slot.x + slot.width and
               y >= slot.y and y <= slot.y + slot.height then
                -- Always select the clicked slot
                for j, otherSlot in ipairs(self.mainMenu.playerSlots) do
                    otherSlot.selected = (j == i)
                end
                self.mainMenu.selectedSlot = i
                -- For local saves, open a selector popup
                if self.mainMenu.allLocal and #self.mainMenu.allLocal > 0 then
                    -- Prepare names list and remember which slot was clicked
                    local names = {}
                    for _, c in ipairs(self.mainMenu.allLocal) do table.insert(names, c.name or "Unknown") end
                    self.mainMenu.popupSlotIndex = i
                    self:showPopup("selectCharacter", "Choose Character", names)
                    return true
                end
                -- Immediate load only for NFC (db) slots
                if slot.source == "db" then
                    self:loadOrCreatePlayer(i)
                end
                return true
            end
        end

        -- Check main menu buttons clicks (unchanged logic for buttons)
        for i, button in ipairs(self.mainMenu.buttons) do
            if x >= button.x and x <= button.x + button.width and
               y >= button.y and y <= button.y + button.height then
                if button.action then
                    button.action()
                    return true
                end
            end
        end
    end
    return false
end

-- Draw character creation form
function UISystem:drawCharacterCreation()
    -- Only draw when active
    if not self.characterCreation.active then return end
    local form = self.components.forms.characterCreation
    if not form then return end

    -- Draw form background
    love.graphics.setColor(0.2, 0.2, 0.2, 0.9)
    love.graphics.rectangle("fill", form.x, form.y, form.width, form.height, 10, 10)

    -- Draw title
    love.graphics.setFont(self.titleFont)
    love.graphics.setColor(0.9, 0.9, 0.9)
    love.graphics.print("Create Character", form.x + 20, form.y + 20)

    -- Draw tab buttons
    local tabWidth = 120
    local tabHeight = 40
    local tabX = form.x + 30
    local tabY = form.y + 70
    local tabSpacing = 10

    for i, tabName in ipairs(self.characterCreation.tabs) do
        local isActive = (tabName == self.characterCreation.currentTab)
        local button = self.characterCreation.buttons[tabName]

        -- Update button position if needed
        if not button then
            button = {
                x = tabX + (i-1) * (tabWidth + tabSpacing),
                y = tabY,
                width = tabWidth,
                height = tabHeight,
                text = tabName:gsub("^%l", string.upper), -- Capitalize first letter
                color = {0.3, 0.3, 0.4},
                hoverColor = {0.4, 0.4, 0.5},
                textColor = {1, 1, 1},
                hovered = false,
                action = function()
                    self.characterCreation.currentTab = tabName
                    self:updateClassPreview()
                    self:updateKitPreview()
                    self:addMessage("Switched to " .. tabName .. " tab", 1)
                end
            }
            self.characterCreation.buttons[tabName] = button
        end

        -- Draw tab background with hover effect
        if isActive then
            love.graphics.setColor(0.4, 0.4, 0.5, 0.9)
        elseif button.hovered then
            love.graphics.setColor(0.35, 0.35, 0.45, 0.8)
        else
            love.graphics.setColor(0.3, 0.3, 0.4, 0.7)
        end
        love.graphics.rectangle("fill", button.x, button.y, button.width, button.height, 5, 5)

        -- Draw tab text
        love.graphics.setFont(self.largeFont)
        love.graphics.setColor(1, 1, 1, isActive and 1 or 0.7)
        local textWidth = self.largeFont:getWidth(button.text)
        love.graphics.print(button.text, button.x + (button.width - textWidth) / 2, button.y + 8)

        -- Draw highlight border if hovered
        if button.hovered and not isActive then
            love.graphics.setColor(0.6, 0.6, 0.8, 0.7)
            love.graphics.rectangle("line", button.x, button.y, button.width, button.height, 5, 5)
        end
    end

    -- Draw content area - larger with more space for content
    love.graphics.setColor(0.3, 0.3, 0.4, 0.8)
    love.graphics.rectangle("fill", form.x + 30, form.y + 120, form.width - 60, form.height - 200, 10, 10)

    -- Draw tab content based on current tab
    if self.characterCreation.currentTab == "basic" then
        self:drawBasicTab(form)
    elseif self.characterCreation.currentTab == "class" then
        self:drawClassTab(form)
    elseif self.characterCreation.currentTab == "skills" then
        self:drawSkillsTab(form)
    elseif self.characterCreation.currentTab == "equipment" then
        self:drawEquipmentTab(form)
    end

    -- Draw Create/Back buttons at bottom with hover effects
    for buttonName, btn in pairs(form.buttons) do
        -- Apply hover effect
        if btn.hovered then
            love.graphics.setColor(btn.hoverColor or {0.4, 0.4, 0.5})
        else
            love.graphics.setColor(btn.color or {0.3, 0.3, 0.4})
        end

        -- Draw button background
        love.graphics.rectangle("fill", btn.x, btn.y, btn.width, btn.height, 8, 8)

        -- Draw button text
        love.graphics.setFont(self.largeFont)
        love.graphics.setColor(btn.textColor or {1, 1, 1})
        local textWidth = self.largeFont:getWidth(btn.text)
        love.graphics.print(btn.text, btn.x + (btn.width - textWidth) / 2, btn.y + (btn.height - self.largeFont:getHeight()) / 2)

        -- Draw highlight border if hovered
        if btn.hovered then
            love.graphics.setColor(0.6, 0.6, 0.8, 0.7)
            love.graphics.rectangle("line", btn.x, btn.y, btn.width, btn.height, 8, 8)
        end

        -- Add debug text to show button name and position
        if Engine.debug or Engine.debugMode then
            love.graphics.setColor(1, 1, 1, 0.7)
            love.graphics.setFont(self.font)
            love.graphics.print(buttonName, btn.x + 5, btn.y + 5)
            love.graphics.print(string.format("x=%d,y=%d", btn.x, btn.y), btn.x + 5, btn.y + 25)
        end
    end
end

-- Draw basic tab content
function UISystem:drawBasicTab(form)
    -- Draw fields for the basic tab
    for _, key in ipairs({"name", "gender"}) do
        local field = form.fields[key]
        if field and field.tab == "basic" then
            self:drawField(field)
        end
    end

    -- Draw character appearance preview - larger and more detailed
    local previewX = form.x + form.width - 400
    local previewY = form.y + 150
    local previewWidth = 320
    local previewHeight = 350

    love.graphics.setColor(0.2, 0.2, 0.3, 0.8)
    love.graphics.rectangle("fill", previewX, previewY, previewWidth, previewHeight, 10, 10)

    love.graphics.setColor(0.9, 0.9, 0.9)
    love.graphics.setFont(self.largeFont)
    love.graphics.print("Character Appearance", previewX + 20, previewY + 20)

    -- Load anime renderer if not already loaded
    if not self.animeRenderer then
        self.animeRenderer = require("utils.anime_renderer")
        self.animeRenderer.init()
    end

    -- Get appearance data
    local appearance = self.characterCreation.data.appearance
    local gender = self.characterCreation.data.gender
    local hairStyle = appearance.hair.style
    local hairColor = appearance.hair.color
    local skinTone = appearance.skin.tone
    local eyeColor = appearance.eyes.color

    -- Map string values to numeric indices for the renderer
    local hairStyleMap = {
        short = 1,
        medium = 2,
        long = 3,
        spiky = 4,
        curly = 5
    }

    local eyeStyleMap = {
        standard = 1,
        large = 2,
        sharp = 3,
        shonen = 4,
        fireforce = 5
    }

    -- Create appearance data for the renderer
    local renderAppearance = {
        head = appearance.head or 1,
        hair = {
            style = hairStyleMap[hairStyle] or 1,
            color = hairColor or "brown"
        },
        skin = skinTone or "medium",
        eyes = {
            style = eyeStyleMap[appearance.eyes.style or "standard"] or 1,
            color = eyeColor or "blue"
        },
        mouth = appearance.mouth or 1,
        body = appearance.body or { type = "average" },
        outfit = appearance.outfit or 1
    }

    -- Draw the character in the center of the preview area
    self.animeRenderer.drawCharacter(
        previewX + previewWidth/2,
        previewY + previewHeight/2,
        1.5,
        gender,
        renderAppearance
    )

    -- Convert appearance options to colors
    local skinColors = {
        fair = {0.96, 0.80, 0.69},
        medium = {0.85, 0.65, 0.45},
        tan = {0.78, 0.57, 0.35},
        dark = {0.55, 0.37, 0.23},
        pale = {0.98, 0.88, 0.82},
        olive = {0.78, 0.70, 0.50},
        ebony = {0.35, 0.22, 0.15}
    }

    local hairColors = {
        black = {0.1, 0.1, 0.1},
        brown = {0.4, 0.25, 0.15},
        blonde = {0.9, 0.8, 0.5},
        red = {0.7, 0.3, 0.2},
        gray = {0.7, 0.7, 0.7},
        white = {0.95, 0.95, 0.95},
        blue = {0.3, 0.4, 0.8},
        green = {0.3, 0.7, 0.4},
        purple = {0.6, 0.3, 0.8}
    }

    local eyeColors = {
        blue = {0.3, 0.5, 0.9},
        green = {0.3, 0.7, 0.4},
        brown = {0.5, 0.3, 0.2},
        gray = {0.6, 0.6, 0.6},
        amber = {0.8, 0.6, 0.2},
        purple = {0.6, 0.3, 0.8},
        hazel = {0.6, 0.5, 0.3}
    }

    -- Get color values or use defaults
    local skinColorValues = skinColors[skinTone] or skinColors.medium
    local hairColorValues = hairColors[hairColor] or hairColors.brown
    local eyeColorValues = eyeColors[eyeColor] or eyeColors.brown

    -- We don't need to draw the character silhouette manually anymore
    -- since we're using the anime_renderer module to draw the character

    -- Add appearance customization info
    love.graphics.setColor(0.9, 0.9, 0.9)
    love.graphics.setFont(self.font)

    -- Display current appearance settings
    local infoX = previewX + 20
    local infoY = previewY + previewHeight - 100

    love.graphics.print("Gender: " .. gender, infoX, infoY)
    love.graphics.print("Hair: " .. hairStyle .. " (" .. hairColor .. ")", infoX, infoY + 20)
    love.graphics.print("Skin: " .. skinTone, infoX, infoY + 40)
    love.graphics.print("Eyes: " .. appearance.eyes.style .. " (" .. eyeColor .. ")", infoX, infoY + 60)

    -- Reset color
    love.graphics.setColor(1, 1, 1)

    -- Reset line width
    love.graphics.setLineWidth(1)

    -- Draw appearance options section with interactive buttons
    local optionsX = form.x + 80
    local optionsY = form.y + 300
    local optionsWidth = 400
    local optionsHeight = 250

    love.graphics.setColor(0.2, 0.2, 0.3, 0.8)
    love.graphics.rectangle("fill", optionsX, optionsY, optionsWidth, optionsHeight, 10, 10)

    love.graphics.setColor(0.9, 0.9, 0.9)
    love.graphics.setFont(self.largeFont)
    love.graphics.print("Appearance Options", optionsX + 20, optionsY + 20)

    -- Draw appearance options with buttons
    love.graphics.setFont(self.font)

    -- Head Style
    love.graphics.print("Head Style:", optionsX + 30, optionsY + 50)
    self:drawAppearanceButton(optionsX + 150, optionsY + 45, 40, 30, "<", "head_style_prev")
    local headStyleNames = {"Standard", "Shonen", "Fire Force", "Minimal", "Detailed", "Sailor Moon", "Cowboy Bebop", "Tenchi Muyo"}
    local headStyle = headStyleNames[appearance.head or 1] or "Standard"
    love.graphics.print(headStyle, optionsX + 200, optionsY + 50)
    self:drawAppearanceButton(optionsX + 300, optionsY + 45, 40, 30, ">", "head_style_next")

    -- Hair Style
    love.graphics.print("Hair Style:", optionsX + 30, optionsY + 85)
    self:drawAppearanceButton(optionsX + 150, optionsY + 80, 40, 30, "<", "hair_style_prev")
    love.graphics.print(hairStyle:gsub("^%l", string.upper), optionsX + 200, optionsY + 85)
    self:drawAppearanceButton(optionsX + 300, optionsY + 80, 40, 30, ">", "hair_style_next")

    -- Hair Color
    love.graphics.print("Hair Color:", optionsX + 30, optionsY + 120)
    self:drawAppearanceButton(optionsX + 150, optionsY + 115, 40, 30, "<", "hair_color_prev")
    love.graphics.print(hairColor:gsub("^%l", string.upper), optionsX + 200, optionsY + 120)
    self:drawAppearanceButton(optionsX + 300, optionsY + 115, 40, 30, ">", "hair_color_next")

    -- Skin Tone
    love.graphics.print("Skin Tone:", optionsX + 30, optionsY + 155)
    self:drawAppearanceButton(optionsX + 150, optionsY + 150, 40, 30, "<", "skin_tone_prev")
    love.graphics.print(skinTone:gsub("^%l", string.upper), optionsX + 200, optionsY + 155)
    self:drawAppearanceButton(optionsX + 300, optionsY + 150, 40, 30, ">", "skin_tone_next")

    -- Eye Style
    love.graphics.print("Eye Style:", optionsX + 30, optionsY + 190)
    self:drawAppearanceButton(optionsX + 150, optionsY + 185, 40, 30, "<", "eye_style_prev")
    local eyeStyleNames = {"Standard", "Large", "Sharp", "Shonen", "Fire Force", "Sailor Moon", "Cowboy Bebop", "Tenchi Muyo"}
    local eyeStyle = eyeStyleNames[eyeStyleMap[appearance.eyes.style or "standard"] or 1] or "Standard"
    love.graphics.print(eyeStyle, optionsX + 200, optionsY + 190)
    self:drawAppearanceButton(optionsX + 300, optionsY + 185, 40, 30, ">", "eye_style_next")

    -- Eye Color
    love.graphics.print("Eye Color:", optionsX + 30, optionsY + 225)
    self:drawAppearanceButton(optionsX + 150, optionsY + 220, 40, 30, "<", "eye_color_prev")
    love.graphics.print(eyeColor:gsub("^%l", string.upper), optionsX + 200, optionsY + 225)
    self:drawAppearanceButton(optionsX + 300, optionsY + 220, 40, 30, ">", "eye_color_next")

    -- Body Type
    love.graphics.print("Body Type:", optionsX + 30, optionsY + 260)
    self:drawAppearanceButton(optionsX + 150, optionsY + 255, 40, 30, "<", "body_type_prev")
    local bodyType = "Average"
    if type(appearance.body) == "table" then
        bodyType = appearance.body.type:gsub("^%l", string.upper)
    end
    love.graphics.print(bodyType, optionsX + 200, optionsY + 260)
    self:drawAppearanceButton(optionsX + 300, optionsY + 255, 40, 30, ">", "body_type_next")

    -- Color preview swatches
    -- Hair color swatch
    love.graphics.setColor(hairColorValues)
    love.graphics.rectangle("fill", optionsX + 350, optionsY + 115, 30, 30)

    -- Skin color swatch
    love.graphics.setColor(skinColorValues)
    love.graphics.rectangle("fill", optionsX + 350, optionsY + 150, 30, 30)

    -- Eye color swatch
    love.graphics.setColor(eyeColorValues)
    love.graphics.rectangle("fill", optionsX + 350, optionsY + 220, 30, 30)
end

-- Helper function to draw appearance option buttons
function UISystem:drawAppearanceButton(x, y, width, height, text, id)
    -- Store button data for click handling
    if not self.characterCreation.appearanceButtons then
        self.characterCreation.appearanceButtons = {}
    end

    -- Check if mouse is hovering over this button
    local mx, my = love.mouse.getPosition()
    local hovered = mx >= x and mx <= x + width and my >= y and my <= y + height

    -- Create or update button
    self.characterCreation.appearanceButtons[id] = {
        x = x,
        y = y,
        width = width,
        height = height,
        text = text,
        id = id,
        hovered = hovered
    }

    -- Draw button with hover effect
    if hovered then
        love.graphics.setColor(0.4, 0.4, 0.6, 0.9)
    else
        love.graphics.setColor(0.3, 0.3, 0.4, 0.8)
    end
    love.graphics.rectangle("fill", x, y, width, height, 5, 5)

    -- Draw border
    love.graphics.setColor(0.5, 0.5, 0.7, 0.8)
    love.graphics.rectangle("line", x, y, width, height, 5, 5)

    -- Draw text
    love.graphics.setColor(1, 1, 1)
    love.graphics.setFont(self.font)
    local textWidth = self.font:getWidth(text)
    love.graphics.print(text, x + (width - textWidth) / 2, y + 5)

    -- Debug info - always show button ID for easier debugging
    love.graphics.setColor(1, 1, 1, 0.5)
    love.graphics.setFont(self.smallFont or self.font)
    love.graphics.print(id, x + 2, y + height - 12)

    -- Print debug info when button is created
    if not self.characterCreation.debuggedButtons then
        self.characterCreation.debuggedButtons = {}
    end

    if not self.characterCreation.debuggedButtons[id] then
        print(string.format("Created appearance button: %s at x=%d, y=%d, w=%d, h=%d",
            id, x, y, width, height))
        self.characterCreation.debuggedButtons[id] = true
    end
end

-- Helper function to cycle through appearance options
function UISystem:cycleAppearanceOption(category, property, direction)
    -- Get the current value
    local currentValue
    if category == "head" then
        currentValue = self.characterCreation.data.appearance.head
    elseif category == "hair" then
        if property == "style" then
            currentValue = self.characterCreation.data.appearance.hair.style
        elseif property == "color" then
            currentValue = self.characterCreation.data.appearance.hair.color
        end
    elseif category == "skin" then
        if property == "tone" then
            currentValue = self.characterCreation.data.appearance.skin.tone
        end
    elseif category == "eyes" then
        if property == "style" then
            currentValue = self.characterCreation.data.appearance.eyes.style
        elseif property == "color" then
            currentValue = self.characterCreation.data.appearance.eyes.color
        end
    elseif category == "mouth" then
        currentValue = self.characterCreation.data.appearance.mouth
    elseif category == "body" then
        if type(self.characterCreation.data.appearance.body) == "table" then
            currentValue = self.characterCreation.data.appearance.body.type
        else
            currentValue = self.characterCreation.data.appearance.body
        end
    elseif category == "outfit" then
        currentValue = self.characterCreation.data.appearance.outfit
    end

    -- Get the options array or max value
    local options
    local maxValue

    if category == "head" then
        -- Numeric value from 1 to 5
        maxValue = 5
    elseif category == "hair" then
        if property == "style" then
            options = self.characterCreation.appearanceOptions.hair.styles
        elseif property == "color" then
            options = self.characterCreation.appearanceOptions.hair.colors
        end
    elseif category == "skin" then
        if property == "tone" then
            options = self.characterCreation.appearanceOptions.skin.tones
        end
    elseif category == "eyes" then
        if property == "style" then
            -- Map string values to numeric indices
            local eyeStyleMap = {
                standard = 1,
                large = 2,
                sharp = 3,
                shonen = 4,
                fireforce = 5,
                sailormoon = 6,
                cowboybebop = 7,
                tenchi = 8
            }

            -- Reverse map for lookup
            local reverseMap = {}
            for k, v in pairs(eyeStyleMap) do
                reverseMap[v] = k
            end

            -- If current value is a string, convert to index
            if type(currentValue) == "string" then
                currentValue = eyeStyleMap[currentValue] or 1
            end

            maxValue = 5
        elseif property == "color" then
            options = self.characterCreation.appearanceOptions.eyes.colors
        end
    elseif category == "mouth" then
        -- Numeric value from 1 to 3
        maxValue = 3
    elseif category == "body" then
        -- Body types
        options = {"thin", "average", "muscular", "heavy", "athletic", "shonen"}
    elseif category == "outfit" then
        -- Numeric value from 1 to 4
        maxValue = 4
    end

    -- Calculate the new value
    local newValue

    if options then
        -- For string-based options
        -- Find the current index
        local currentIndex = 1
        for i, value in ipairs(options) do
            if value == currentValue then
                currentIndex = i
                break
            end
        end

        -- Calculate the new index
        local newIndex = currentIndex + direction
        if newIndex < 1 then
            newIndex = #options
        elseif newIndex > #options then
            newIndex = 1
        end

        newValue = options[newIndex]
    else
        -- For numeric options
        newValue = currentValue + direction
        if newValue < 1 then
            newValue = maxValue
        elseif newValue > maxValue then
            newValue = 1
        end
    end

    -- Update the value
    if category == "head" then
        self.characterCreation.data.appearance.head = newValue
    elseif category == "hair" then
        if property == "style" then
            self.characterCreation.data.appearance.hair.style = newValue
        elseif property == "color" then
            self.characterCreation.data.appearance.hair.color = newValue
        end
    elseif category == "skin" then
        if property == "tone" then
            self.characterCreation.data.appearance.skin.tone = newValue
        end
    elseif category == "eyes" then
        if property == "style" then
            -- Convert numeric index back to string name
            local eyeStyleNames = {"standard", "large", "sharp", "shonen", "fireforce", "sailormoon", "cowboybebop", "tenchi"}
            self.characterCreation.data.appearance.eyes.style = eyeStyleNames[newValue]
        elseif property == "color" then
            self.characterCreation.data.appearance.eyes.color = newValue
        end
    elseif category == "mouth" then
        self.characterCreation.data.appearance.mouth = newValue
    elseif category == "body" then
        -- Ensure body is a table
        if type(self.characterCreation.data.appearance.body) ~= "table" then
            self.characterCreation.data.appearance.body = { type = "average" }
        end
        -- Update the body type
        self.characterCreation.data.appearance.body.type = newValue
    elseif category == "outfit" then
        self.characterCreation.data.appearance.outfit = newValue
    end

    -- Debug output
    print(string.format("Changed %s %s from %s to %s", category, property or "", tostring(currentValue), tostring(newValue)))
end

-- Draw class tab content
function UISystem:drawClassTab(form)
    -- Draw class selection field
    local field = form.fields.class
    if field then
        self:drawField(field)
    end

    -- Get class information
    local classes = Engine.systems.characterCreator.getAvailableClasses() or {}
    local selectedClass = classes[self.characterCreation.data.class] or "Unknown"
    local classInfo = nil
    if Engine.systems.characterCreator.getClassInfo then
        classInfo = Engine.systems.characterCreator.getClassInfo(selectedClass)
    end

    -- Draw class info panel - larger and more detailed
    local infoX = form.x + 80
    local infoY = form.y + 220
    local infoWidth = 500
    local infoHeight = 300

    love.graphics.setColor(0.2, 0.2, 0.3, 0.8)
    love.graphics.rectangle("fill", infoX, infoY, infoWidth, infoHeight, 10, 10)

    love.graphics.setColor(0.9, 0.9, 0.9)
    love.graphics.setFont(self.titleFont)
    love.graphics.print(selectedClass, infoX + 20, infoY + 20)

    love.graphics.setFont(self.largeFont)
    love.graphics.setColor(0.8, 0.9, 1.0)
    love.graphics.print("Class Statistics", infoX + 20, infoY + 80)

    love.graphics.setFont(self.font)
    love.graphics.setColor(0.9, 0.9, 0.9)
    love.graphics.print("Health Points: " .. (classInfo and classInfo.startingHP or "100"), infoX + 40, infoY + 120)
    love.graphics.print("Mana Points: " .. (classInfo and classInfo.startingMP or "50"), infoX + 40, infoY + 150)
    love.graphics.print("Primary Attribute: " .. (classInfo and classInfo.primaryStat or "Strength"), infoX + 40, infoY + 180)

    -- Draw stat bars
    local barWidth = 200
    local barHeight = 15

    -- HP bar
    love.graphics.setColor(0.3, 0.3, 0.3)
    love.graphics.rectangle("fill", infoX + 250, infoY + 120, barWidth, barHeight)
    love.graphics.setColor(0.8, 0.2, 0.2)
    local hpValue = classInfo and classInfo.startingHP or 100
    local hpRatio = math.min(1, hpValue / 200) -- Assuming 200 is max HP
    love.graphics.rectangle("fill", infoX + 250, infoY + 120, barWidth * hpRatio, barHeight)

    -- MP bar
    love.graphics.setColor(0.3, 0.3, 0.3)
    love.graphics.rectangle("fill", infoX + 250, infoY + 150, barWidth, barHeight)
    love.graphics.setColor(0.2, 0.4, 0.8)
    local mpValue = classInfo and classInfo.startingMP or 50
    local mpRatio = math.min(1, mpValue / 200) -- Assuming 200 is max MP
    love.graphics.rectangle("fill", infoX + 250, infoY + 150, barWidth * mpRatio, barHeight)

    -- Draw class description
    love.graphics.setFont(self.largeFont)
    love.graphics.setColor(0.8, 0.9, 1.0)
    love.graphics.print("Class Description", infoX + 20, infoY + 220)

    love.graphics.setFont(self.font)
    love.graphics.setColor(0.9, 0.9, 0.9)
    local description = "The " .. selectedClass .. " is a powerful adventurer with unique abilities and skills. "
    if selectedClass == "Warrior" then
        description = description .. "Warriors excel in close combat and have high physical defense."
    elseif selectedClass == "Mage" then
        description = description .. "Mages command powerful spells and have high magical abilities."
    elseif selectedClass == "Rogue" then
        description = description .. "Rogues are masters of stealth and precision strikes."
    elseif selectedClass == "Hunter" then
        description = description .. "Hunters are skilled with ranged weapons and can tame wild creatures."
    else
        description = description .. "This class has a unique set of abilities that set them apart."
    end
    love.graphics.printf(description, infoX + 40, infoY + 250, infoWidth - 80, "left")

    -- Draw class icon/preview - larger and more detailed
    local iconX = form.x + form.width - 350
    local iconY = form.y + 150
    local iconSize = 250

    love.graphics.setColor(0.3, 0.3, 0.4, 0.8)
    love.graphics.rectangle("fill", iconX, iconY, iconSize, iconSize, 10, 10)

    -- Draw class symbol based on class type
    love.graphics.setColor(0.9, 0.9, 0.9)
    if selectedClass == "Warrior" then
        -- Draw sword symbol
        love.graphics.setLineWidth(5)
        love.graphics.line(iconX + 125, iconY + 50, iconX + 125, iconY + 180)  -- sword blade
        love.graphics.line(iconX + 85, iconY + 180, iconX + 165, iconY + 180)  -- sword guard
        love.graphics.rectangle("fill", iconX + 115, iconY + 180, 20, 30)      -- sword handle
    elseif selectedClass == "Mage" then
        -- Draw staff/wand symbol
        love.graphics.setLineWidth(5)
        love.graphics.line(iconX + 125, iconY + 50, iconX + 125, iconY + 200)  -- staff
        love.graphics.circle("fill", iconX + 125, iconY + 50, 20)              -- orb
    elseif selectedClass == "Rogue" then
        -- Draw dagger symbol
        love.graphics.setLineWidth(4)
        love.graphics.line(iconX + 105, iconY + 70, iconX + 145, iconY + 180)  -- dagger 1
        love.graphics.line(iconX + 145, iconY + 70, iconX + 105, iconY + 180)  -- dagger 2
    elseif selectedClass == "Hunter" then
        -- Draw bow symbol
        love.graphics.setLineWidth(4)
        love.graphics.arc("line", iconX + 125, iconY + 125, 80, -0.7, 0.7)     -- bow
        love.graphics.line(iconX + 125, iconY + 50, iconX + 125, iconY + 200)  -- arrow
    else
        -- Generic class symbol
        love.graphics.setFont(self.titleFont)
        love.graphics.print(selectedClass:sub(1, 1), iconX + iconSize/2 - 15, iconY + iconSize/2 - 20)
    end
    love.graphics.setLineWidth(1) -- Reset line width
end

-- Draw skills tab content
function UISystem:drawSkillsTab(form)
    -- Get class information
    local classes = Engine.systems.characterCreator.getAvailableClasses() or {}
    local selectedClass = classes[self.characterCreation.data.class] or "Unknown"
    local skills = self:loadClassSkills(selectedClass)

    -- Draw skill points header
    love.graphics.setColor(0.9, 0.9, 0.9)
    love.graphics.setFont(self.titleFont)
    love.graphics.print(selectedClass .. " Skills", form.x + 80, form.y + 130)

    -- Draw available skill points with visual indicator
    local pointsX = form.x + 80
    local pointsY = form.y + 180
    local pointsWidth = 300
    local pointsHeight = 40

    love.graphics.setColor(0.2, 0.2, 0.3, 0.8)
    love.graphics.rectangle("fill", pointsX, pointsY, pointsWidth, pointsHeight, 10, 10)

    love.graphics.setColor(0.9, 0.9, 0.9)
    love.graphics.setFont(self.largeFont)
    love.graphics.print("Available Skill Points: ", pointsX + 20, pointsY + 8)

    -- Draw skill points as circles
    local circleX = pointsX + 250
    local circleY = pointsY + 20
    local circleRadius = 12
    local circleSpacing = 30
    local availablePoints = self.characterCreation.preview.skillPoints or 0

    for i = 1, 5 do -- Assuming max 5 skill points
        if i <= availablePoints then
            love.graphics.setColor(0.3, 0.8, 0.3) -- Green for available points
        else
            love.graphics.setColor(0.4, 0.4, 0.4) -- Gray for used points
        end
        love.graphics.circle("fill", circleX + (i-1) * circleSpacing, circleY, circleRadius)
    end

    -- Draw skills list - larger and more detailed
    local skillsX = form.x + 80
    local skillsY = form.y + 240
    local skillWidth = 500
    local skillHeight = 50
    local skillSpacing = 15

    love.graphics.setFont(self.largeFont)
    love.graphics.setColor(0.8, 0.9, 1.0)
    love.graphics.print("Available Skills", skillsX, skillsY - 40)

    -- Draw skills container
    love.graphics.setColor(0.2, 0.2, 0.3, 0.8)
    love.graphics.rectangle("fill", skillsX, skillsY, skillWidth, 300, 10, 10)

    -- Draw skills
    love.graphics.setFont(self.font)
    for i, skill in ipairs(skills) do
        if i <= 5 then -- Limit to 5 skills to avoid overflow
            local y = skillsY + 20 + (i-1) * (skillHeight + skillSpacing)

            -- Draw skill background
            local isSelected = self.characterCreation.data.selectedSkills[skill.id]
            if isSelected then
                love.graphics.setColor(0.2, 0.5, 0.2, 0.9)
            else
                love.graphics.setColor(0.3, 0.3, 0.4, 0.7)
            end
            love.graphics.rectangle("fill", skillsX + 15, y, skillWidth - 30, skillHeight, 8, 8)

            -- Draw skill name and cost
            if isSelected then
                love.graphics.setColor(1, 1, 1)
            else
                love.graphics.setColor(0.9, 0.9, 0.9)
            end
            local name = skill.name or "Unknown Skill"
            local cost = skill.cost or 1

            -- Draw skill icon (placeholder)
            love.graphics.circle("fill", skillsX + 35, y + skillHeight/2, 15)

            -- Draw skill name
            love.graphics.setFont(self.largeFont)
            love.graphics.print(name, skillsX + 60, y + 5)

            -- Draw skill cost
            love.graphics.setFont(self.font)
            love.graphics.print("Cost: " .. cost .. " point" .. (cost > 1 and "s" or ""), skillsX + 60, y + 30)

            -- Draw select/deselect button
            local buttonX = skillsX + skillWidth - 100
            local buttonY = y + 10
            local buttonWidth = 70
            local buttonHeight = 30

            if isSelected then
                love.graphics.setColor(0.7, 0.2, 0.2)
                love.graphics.rectangle("fill", buttonX, buttonY, buttonWidth, buttonHeight, 5, 5)
                love.graphics.setColor(1, 1, 1)
                love.graphics.print("Remove", buttonX + 10, buttonY + 8)
            else
                love.graphics.setColor(0.2, 0.6, 0.2)
                love.graphics.rectangle("fill", buttonX, buttonY, buttonWidth, buttonHeight, 5, 5)
                love.graphics.setColor(1, 1, 1)
                love.graphics.print("Select", buttonX + 15, buttonY + 8)
            end

            -- Draw tooltip if mouse is over skill
            if self:isMouseOverSkill(skillsX + 15, y, skillWidth - 30, skillHeight) then
                self:drawSkillTooltip(skill, skillsX + skillWidth + 20, y)
            end
        end
    end

    -- Draw selected skills panel - larger and more detailed
    local selectedX = form.x + form.width - 350
    local selectedY = form.y + 240
    local selectedWidth = 250
    local selectedHeight = 300

    love.graphics.setColor(0.2, 0.2, 0.3, 0.8)
    love.graphics.rectangle("fill", selectedX, selectedY, selectedWidth, selectedHeight, 10, 10)

    love.graphics.setColor(0.8, 0.9, 1.0)
    love.graphics.setFont(self.largeFont)
    love.graphics.print("Selected Skills", selectedX + 20, selectedY + 20)

    -- Draw divider
    love.graphics.setColor(0.4, 0.4, 0.5, 0.7)
    love.graphics.rectangle("fill", selectedX + 20, selectedY + 55, selectedWidth - 40, 2)

    -- Draw selected skills list
    love.graphics.setFont(self.font)
    local count = 0
    for id, _ in pairs(self.characterCreation.data.selectedSkills) do
        for _, skill in ipairs(skills) do
            if skill.id == id then
                -- Draw skill entry with icon
                love.graphics.setColor(0.3, 0.6, 0.3, 0.7)
                love.graphics.rectangle("fill", selectedX + 20, selectedY + 70 + count * 45, selectedWidth - 40, 35, 5, 5)

                love.graphics.setColor(1, 1, 1)
                love.graphics.circle("fill", selectedX + 35, selectedY + 87 + count * 45, 10) -- icon
                love.graphics.print(skill.name, selectedX + 55, selectedY + 78 + count * 45)

                count = count + 1
                break
            end
        end
    end

    if count == 0 then
        love.graphics.setColor(0.7, 0.7, 0.7)
        love.graphics.print("No skills selected yet", selectedX + 40, selectedY + 100)
        love.graphics.print("Click 'Select' on a skill to add it", selectedX + 30, selectedY + 130)
    end
end

-- Draw equipment tab content
function UISystem:drawEquipmentTab(form)
    -- Draw kit selection field
    local field = form.fields.kit
    if field then
        self:drawField(field)
    end

    -- Get kit information
    local kits = {}
    for name, _ in pairs(Engine.systems.characterCreator.startingKits) do
        table.insert(kits, name)
    end
    local selectedKit = kits[self.characterCreation.data.kit]
    local kitInfo = Engine.systems.characterCreator.startingKits[selectedKit]

    -- Draw kit title
    love.graphics.setColor(0.9, 0.9, 0.9)
    love.graphics.setFont(self.titleFont)
    love.graphics.print("Starting Equipment", form.x + 80, form.y + 130)

    -- Draw kit info - larger and more detailed
    local infoX = form.x + 80
    local infoY = form.y + 180
    local infoWidth = 500
    local infoHeight = 300

    love.graphics.setColor(0.2, 0.2, 0.3, 0.8)
    love.graphics.rectangle("fill", infoX, infoY, infoWidth, infoHeight, 10, 10)

    love.graphics.setColor(0.8, 0.9, 1.0)
    love.graphics.setFont(self.largeFont)
    love.graphics.print("Kit: " .. selectedKit, infoX + 20, infoY + 20)

    -- Draw kit description
    love.graphics.setFont(self.font)
    love.graphics.setColor(0.9, 0.9, 0.9)
    if kitInfo then
        local description = kitInfo.description or "A standard set of equipment for adventurers."
        if selectedKit == "Warrior" then
            description = "Heavy armor and weapons for close combat specialists."
        elseif selectedKit == "Mage" then
            description = "Magical implements and light armor for spellcasters."
        elseif selectedKit == "Rogue" then
            description = "Light armor and tools for stealth and precision."
        elseif selectedKit == "Hunter" then
            description = "Ranged weapons and survival gear for wilderness experts."
        elseif selectedKit == "Custom" then
            description = "Build your own custom equipment set with starting gold."
        end

        love.graphics.printf(description, infoX + 20, infoY + 60, infoWidth - 40, "left")

        -- Draw divider
        love.graphics.setColor(0.4, 0.4, 0.5, 0.7)
        love.graphics.rectangle("fill", infoX + 20, infoY + 90, infoWidth - 40, 2)

        -- Draw items section
        love.graphics.setColor(0.8, 0.9, 1.0)
        love.graphics.setFont(self.largeFont)
        love.graphics.print("Included Items:", infoX + 20, infoY + 110)

        -- Draw items in a grid layout
        love.graphics.setFont(self.font)
        love.graphics.setColor(0.9, 0.9, 0.9)

        if kitInfo.items then
            local itemX = infoX + 40
            local itemY = infoY + 150
            local itemWidth = 200
            local itemHeight = 60
            local itemsPerRow = 2
            local itemCount = 0

            for itemId, itemData in pairs(kitInfo.items) do
                local col = itemCount % itemsPerRow
                local row = math.floor(itemCount / itemsPerRow)

                local x = itemX + col * (itemWidth + 20)
                local y = itemY + row * (itemHeight + 15)

                -- Draw item box
                love.graphics.setColor(0.3, 0.3, 0.4, 0.7)
                love.graphics.rectangle("fill", x, y, itemWidth, itemHeight, 8, 8)

                -- Draw item icon (placeholder)
                love.graphics.setColor(0.8, 0.8, 0.8)
                love.graphics.circle("fill", x + 20, y + itemHeight/2, 15)

                -- Draw item details
                love.graphics.setColor(1, 1, 1)
                love.graphics.print(itemData.name, x + 45, y + 10)
                love.graphics.setColor(0.8, 0.8, 0.8)
                love.graphics.print("Quantity: " .. itemData.quantity, x + 45, y + 35)

                itemCount = itemCount + 1
                if itemCount >= 6 then break end -- Limit to 6 items to avoid overflow
            end

            if itemCount == 0 then
                love.graphics.print("No items in this kit", infoX + 40, infoY + 150)
            end
        elseif kitInfo.is_custom then
            love.graphics.setColor(1, 1, 0)
            love.graphics.print("Starting Gold: " .. (kitInfo.starting_gold or 0), infoX + 40, infoY + 150)
            love.graphics.setColor(0.9, 0.9, 0.9)
            love.graphics.print("Use your gold to purchase items from the shop", infoX + 40, infoY + 180)
        end
    end

    -- Draw shop panel for Custom kit - larger and more detailed
    if selectedKit == "Custom" then
        local shopX = form.x + form.width - 400
        local shopY = form.y + 180
        local shopWidth = 300
        local shopHeight = 350

        love.graphics.setColor(0.2, 0.2, 0.3, 0.8)
        love.graphics.rectangle("fill", shopX, shopY, shopWidth, shopHeight, 10, 10)

        -- Draw shop header
        love.graphics.setColor(0.8, 0.9, 1.0)
        love.graphics.setFont(self.largeFont)
        love.graphics.print("Equipment Shop", shopX + 20, shopY + 20)

        -- Draw gold amount with coin icon
        love.graphics.setColor(1, 1, 0)
        love.graphics.circle("fill", shopX + 30, shopY + 60, 10) -- coin icon
        love.graphics.setFont(self.largeFont)
        love.graphics.print("Gold: " .. (self.characterCreation.preview.shopGold or 0), shopX + 50, shopY + 50)

        -- Draw divider
        love.graphics.setColor(0.4, 0.4, 0.5, 0.7)
        love.graphics.rectangle("fill", shopX + 20, shopY + 80, shopWidth - 40, 2)

        -- Draw shop instructions
        love.graphics.setColor(0.8, 0.8, 0.8)
        love.graphics.setFont(self.font)
        love.graphics.print("Click an item to purchase it", shopX + 20, shopY + 90)

        -- Build and draw shop list
        self.shopList = {}
        local itemY = shopY + 120
        local itemHeight = 45
        local itemSpacing = 10
        local idx = 1

        for id, data in pairs(self.characterCreation.preview.equipment or {}) do
            if idx <= 5 then -- Limit to 5 items to avoid overflow
                self.shopList[idx] = {id = id, name = data.name, cost = data.cost}

                -- Draw item background
                love.graphics.setColor(0.3, 0.3, 0.4, 0.7)
                love.graphics.rectangle("fill", shopX + 20, itemY, shopWidth - 40, itemHeight, 8, 8)

                -- Draw item icon (placeholder)
                love.graphics.setColor(0.8, 0.8, 0.8)
                love.graphics.circle("fill", shopX + 40, itemY + itemHeight/2, 12)

                -- Draw item name and cost
                love.graphics.setColor(1, 1, 1)
                love.graphics.print(data.name, shopX + 65, itemY + 8)

                love.graphics.setColor(1, 1, 0)
                love.graphics.print(data.cost .. " gold", shopX + 65, itemY + 28)

                -- Draw quantity
                love.graphics.setColor(0.8, 0.8, 1)
                love.graphics.print("x" .. (self.characterCreation.preview.cart[id] or 0), shopX + shopWidth - 50, itemY + itemHeight/2 - 8)

                itemY = itemY + itemHeight + itemSpacing
                idx = idx + 1
            end
        end

        -- Draw cart summary
        love.graphics.setColor(0.3, 0.3, 0.4, 0.7)
        love.graphics.rectangle("fill", shopX + 20, shopY + shopHeight - 70, shopWidth - 40, 50, 8, 8)

        love.graphics.setColor(0.8, 0.9, 1.0)
        love.graphics.setFont(self.font)
        love.graphics.print("Your Cart:", shopX + 30, shopY + shopHeight - 60)

        -- Count total items in cart
        local totalItems = 0
        for _, quantity in pairs(self.characterCreation.preview.cart or {}) do
            totalItems = totalItems + quantity
        end

        love.graphics.setColor(1, 1, 1)
        love.graphics.print("Total Items: " .. totalItems, shopX + 30, shopY + shopHeight - 40)
    end
end

-- Draw a button
function UISystem:drawButton(button)
    -- Set color based on hover state
    local bgColor = button.hovered and {0.4, 0.4, 0.4} or {0.3, 0.3, 0.3}
    if button.color then
        bgColor = button.hovered and (button.hoverColor or {0.4, 0.5, 0.6}) or button.color
    end

    -- Draw button background
    love.graphics.setColor(bgColor[1], bgColor[2], bgColor[3], 0.9)
    love.graphics.rectangle("fill", button.x, button.y, button.width, button.height, 5, 5)

    -- Draw button border
    love.graphics.setColor(0.5, 0.5, 0.5, 0.7)
    love.graphics.rectangle("line", button.x, button.y, button.width, button.height, 5, 5)

    -- Draw button text
    love.graphics.setFont(self.font)
    love.graphics.setColor(button.textColor or {1, 1, 1})

    -- Center text
    local textWidth = self.font:getWidth(button.text)
    love.graphics.print(button.text, button.x + (button.width - textWidth) / 2, button.y + (button.height - self.font:getHeight()) / 2)

    -- Debug outline for button hitbox (only in debug mode)
    if Engine.debug or Engine.debugMode then
        love.graphics.setColor(1, 0, 0, 0.3)
        love.graphics.rectangle("line", button.x, button.y, button.width, button.height)
    end
end

-- Handle text input (for UTF-8 text entry)
-- Note: We use the utf8 library to properly handle non-ASCII characters in player names
-- This allows players to use characters from any language, including emoji and special symbols
function UISystem:textinput(text)
    -- Handle character creation text input
    if self.characterCreation.active and self.characterCreation.activeTextField == "name" then
        local field = self.components.forms.characterCreation.fields.name
        if field then
            local current_value = field.value or ""
            local cursor_pos = self.characterCreation.cursorPos or utf8.len(current_value)

            -- Ensure cursor position is valid
            cursor_pos = math.max(0, math.min(utf8.len(current_value), cursor_pos))

            -- Construct the new value by inserting text at the cursor position
            local text_before_cursor = utf8.sub(current_value, 1, cursor_pos)
            local text_after_cursor = utf8.sub(current_value, cursor_pos + 1)
            local new_value = text_before_cursor .. text .. text_after_cursor

            -- Limit name length to 20 characters (using utf8.len)
            if utf8.len(new_value) <= 20 then
                field.value = new_value
                self.characterCreation.data.name = new_value
                -- Move cursor position forward by the length of the inserted text
                self.characterCreation.cursorPos = cursor_pos + utf8.len(text)
            end
        end
    end
end

-- Draw a form field
function UISystem:drawField(field)
    -- Special handling for text input fields (name field)
    if field.tab == "basic" and field.label == "Character Name" then
        -- Draw field label
        love.graphics.setFont(self.largeFont)
        love.graphics.setColor(0.9, 0.9, 0.9)
        love.graphics.print(field.label, field.x, field.y - 30)

        -- Draw text input box
        if self.characterCreation.activeTextField == "name" then
            -- Active text field - brighter background
            love.graphics.setColor(0.3, 0.3, 0.4, 0.9)
        else
            -- Inactive text field
            love.graphics.setColor(0.25, 0.25, 0.3, 0.9)
        end

        -- Draw text input background
        love.graphics.rectangle("fill", field.x, field.y, field.width, field.height, 5, 5)

        -- Draw border - highlighted when active
        if self.characterCreation.activeTextField == "name" then
            love.graphics.setColor(0.6, 0.6, 0.8, 0.9) -- Brighter border when active
            love.graphics.setLineWidth(2)
        else
            love.graphics.setColor(0.4, 0.4, 0.5, 0.7)
            love.graphics.setLineWidth(1)
        end
        love.graphics.rectangle("line", field.x, field.y, field.width, field.height, 5, 5)
        love.graphics.setLineWidth(1) -- Reset line width

        -- Draw text value
        local value = field.value or ""
        local is_placeholder = (value == "Player")
        local is_active = (self.characterCreation.activeTextField == "name")

        -- Set text color based on state
        if is_active then
            love.graphics.setColor(1, 1, 0.8) -- Slightly yellow for active field
        elseif is_placeholder then
            love.graphics.setColor(0.7, 0.7, 0.7) -- Gray for placeholder
        else
            love.graphics.setColor(1, 1, 1) -- White for user input
        end

        -- Draw the text
        love.graphics.setFont(self.font)
        love.graphics.print(value, field.x + 15, field.y + (field.height - self.font:getHeight()) / 2)

        -- Draw text cursor only when active
        if is_active then
            local cursorVisible = math.floor(love.timer.getTime() * 2) % 2 == 0
            if cursorVisible then
                local cursorPos = self.characterCreation.cursorPos or utf8.len(value)
                -- Ensure cursor position is valid within the current text length
                cursorPos = math.max(0, math.min(utf8.len(value), cursorPos))
                local textBeforeCursor = utf8.sub(value, 1, cursorPos)
                local textWidth = self.font:getWidth(textBeforeCursor)
                local textX = field.x + 15
                local textY = field.y + (field.height - self.font:getHeight()) / 2
                love.graphics.setColor(1, 1, 1) -- White cursor
                love.graphics.rectangle("fill", textX + textWidth, textY, 2, self.font:getHeight())
            end
        end
    else
        -- Standard field drawing for non-text input fields
        -- Draw field background
        love.graphics.setColor(0.25, 0.25, 0.3, 0.9)
        love.graphics.rectangle("fill", field.x, field.y, field.width, field.height, 5, 5)

        -- Draw field border
        love.graphics.setColor(0.4, 0.4, 0.5, 0.7)
        love.graphics.rectangle("line", field.x, field.y, field.width, field.height, 5, 5)

        -- Draw field label
        love.graphics.setFont(self.font)
        love.graphics.setColor(0.9, 0.9, 0.9)
        love.graphics.print(field.label, field.x + 10, field.y + (field.height - self.font:getHeight()) / 2)

        -- Draw field value
        local value = field.value
        if field.options then
            if type(field.value) == "number" then
                value = field.options[field.value]
            else
                value = field.value
            end
        end

        love.graphics.setColor(1, 1, 1)
        love.graphics.print(value, field.x + field.width - 150, field.y + (field.height - self.font:getHeight()) / 2)
    end
end

-- Draw popup
function UISystem:drawPopup()
    local centerX = self.screenWidth / 2
    local centerY = self.screenHeight / 2

    -- Draw popup background
    love.graphics.setColor(0.1, 0.1, 0.1, 0.9)
    love.graphics.rectangle("fill", centerX - 200, centerY - 150, 400, 300)

    -- Draw popup title
    love.graphics.setFont(self.largeFont)
    love.graphics.setColor(1, 1, 1)
    love.graphics.print(self.popup.title, centerX - 100, centerY - 120)

    -- Draw popup items
    for i, item in ipairs(self.popup.items) do
        local color = i == self.popup.selectedItem and {1, 1, 0} or {1, 1, 1}
        love.graphics.setColor(color)
        love.graphics.print(item, centerX - 180, centerY - 80 + (i-1) * 30)
    end
end

-- New function to handle back button click
function UISystem:handleBackButtonClick()
    print("BACK BUTTON CLICKED - DIRECT HANDLER")

    -- Disable character creation
    self.characterCreation.active = false

    -- Enable main menu
    self.mainMenuActive = true

    -- Clean up
    self.characterCreation.slotToFill = nil
    self.characterCreation.activeTextField = nil
    self.characterCreation.cursorPos = nil

    -- Disable text input
    love.keyboard.setTextInput(false)

    print("Returned to main menu")
end

-- Handle mouse clicks
function UISystem:mousepressed(x, y, button)
    print("UISystem:mousepressed called at " .. x .. ", " .. y .. ", button: " .. button)

    if button ~= 1 then return end

    -- Let debug systems handle mouse clicks first
    if EventLogViewer.mousePressed(x, y, button) then
        return
    end

    if MemoryMonitor.mousePressed(x, y, button) then
        return
    end

    -- Check for back button in character creation first (direct fix)
    if self.characterCreation.active then
        local form = self.components.forms and self.components.forms.characterCreation
        if form and form.buttons and form.buttons.back then
            local btn = form.buttons.back
            if x >= btn.x and x <= btn.x + btn.width and
               y >= btn.y and y <= btn.y + btn.height then
                print("DIRECT FIX: Back button clicked in character creation")
                self:handleBackButtonClick()
                return
            end
        end

        -- Direct handler for appearance buttons
        if self.characterCreation.currentTab == "basic" and self.characterCreation.appearanceButtons then
            for id, btn in pairs(self.characterCreation.appearanceButtons) do
                if x >= btn.x and x <= btn.x + btn.width and
                   y >= btn.y and y <= btn.y + btn.height then

                    print("DIRECT FIX: Appearance button clicked: " .. id)

                    -- Handle different appearance buttons
                    if id == "head_style_prev" then
                        print("Cycling head style backward")
                        self:cycleAppearanceOption("head", nil, -1)
                        return
                    elseif id == "head_style_next" then
                        print("Cycling head style forward")
                        self:cycleAppearanceOption("head", nil, 1)
                        return
                    elseif id == "hair_style_prev" then
                        print("Cycling hair style backward")
                        self:cycleAppearanceOption("hair", "style", -1)
                        return
                    elseif id == "hair_style_next" then
                        print("Cycling hair style forward")
                        self:cycleAppearanceOption("hair", "style", 1)
                        return
                    elseif id == "hair_color_prev" then
                        print("Cycling hair color backward")
                        self:cycleAppearanceOption("hair", "color", -1)
                        return
                    elseif id == "hair_color_next" then
                        print("Cycling hair color forward")
                        self:cycleAppearanceOption("hair", "color", 1)
                        return
                    elseif id == "skin_tone_prev" then
                        print("Cycling skin tone backward")
                        self:cycleAppearanceOption("skin", "tone", -1)
                        return
                    elseif id == "skin_tone_next" then
                        print("Cycling skin tone forward")
                        self:cycleAppearanceOption("skin", "tone", 1)
                        return
                    elseif id == "eye_style_prev" then
                        print("Cycling eye style backward")
                        self:cycleAppearanceOption("eyes", "style", -1)
                        return
                    elseif id == "eye_style_next" then
                        print("Cycling eye style forward")
                        self:cycleAppearanceOption("eyes", "style", 1)
                        return
                    elseif id == "eye_color_prev" then
                        print("Cycling eye color backward")
                        self:cycleAppearanceOption("eyes", "color", -1)
                        return
                    elseif id == "eye_color_next" then
                        print("Cycling eye color forward")
                        self:cycleAppearanceOption("eyes", "color", 1)
                        return
                    elseif id == "body_type_prev" then
                        print("Cycling body type backward")
                        self:cycleAppearanceOption("body", nil, -1)
                        return
                    elseif id == "body_type_next" then
                        print("Cycling body type forward")
                        self:cycleAppearanceOption("body", nil, 1)
                        return
                    end
                end
            end
        end

        print("Handling character creation click")
        self:handleCharacterCreationClick(x, y)
    else
        print("Handling main menu click")
        self:handleMainMenuClick(x, y)
    end
end

-- Handle character creation clicks
function UISystem:handleCharacterCreationClick(x, y)
    if not self.characterCreation.active then return end
    local form = self.components.forms and self.components.forms.characterCreation
    if not form then return false end

    -- Debug output for click coordinates
    print("Character creator click at: " .. x .. ", " .. y)

    -- Debug UI elements to check z-order and clickability
    self:debugUIElements(x, y)

    -- Debug the form buttons
    local form = self.components.forms and self.components.forms.characterCreation
    if form and form.buttons then
        print("Character creation form buttons:")
        for buttonName, btn in pairs(form.buttons) do
            print(string.format("Button: %s, Position: x=%d, y=%d, width=%d, height=%d",
                buttonName, btn.x, btn.y, btn.width, btn.height))
        end
    end

    -- Handle tab button clicks first (they're on top)
    for _, tabName in ipairs(self.characterCreation.tabs) do
        local button = self.characterCreation.buttons[tabName]
        if button then
            -- Check if click is within button bounds
            if x >= button.x and x <= button.x + button.width and
               y >= button.y and y <= button.y + button.height then
                -- Debug message to confirm tab button click
                print("Character creator tab button clicked: " .. tabName)

                -- Execute the button's action if it exists
                if button.action then
                    print("Executing tab button action for: " .. tabName)
                    button.action()
                else
                    -- Fallback if no action is defined
                    self.characterCreation.currentTab = tabName
                    self:updateClassPreview()
                    self:updateKitPreview()
                    self:addMessage("Switched to " .. tabName .. " tab", 1)
                end

                return true
            end
        end
    end

    -- Handle form buttons (Create, Back)
    if form.buttons then
        for buttonName, btn in pairs(form.buttons) do
            if x >= btn.x and x <= btn.x + btn.width and
               y >= btn.y and y <= btn.y + btn.height then
                -- Debug message to confirm button click
                print("Character creator button clicked: " .. buttonName .. " at x=" .. btn.x .. ", y=" .. btn.y)

                -- Handle back button directly
                if buttonName == "back" then
                    print("BACK BUTTON CLICKED - CALLING DIRECT HANDLER")
                    self:handleBackButtonClick()
                    return true
                elseif buttonName == "create" then
                    self:createCharacter()
                    return true
                end

                -- Fallback to action if defined
                if btn.action then
                    print("Executing button action for: " .. buttonName)
                    btn.action()
                    return true
                end

                return true
            end
        end
    end

    -- Handle appearance buttons in basic tab
    if self.characterCreation.currentTab == "basic" and self.characterCreation.appearanceButtons then
        print("Checking appearance buttons. Current tab: " .. self.characterCreation.currentTab)
        -- Count appearance buttons
        local buttonCount = 0
        for _ in pairs(self.characterCreation.appearanceButtons) do
            buttonCount = buttonCount + 1
        end
        print("Number of appearance buttons: " .. buttonCount)

        -- Debug: Print all appearance buttons
        for id, btn in pairs(self.characterCreation.appearanceButtons) do
            print(string.format("Button %s: x=%d, y=%d, w=%d, h=%d",
                id, btn.x, btn.y, btn.width, btn.height))
        end

        -- Check if click is within any appearance button
        for id, btn in pairs(self.characterCreation.appearanceButtons) do
            if x >= btn.x and x <= btn.x + btn.width and
               y >= btn.y and y <= btn.y + btn.height then

                print("APPEARANCE BUTTON CLICKED: " .. id)

                -- Handle different appearance buttons
                if id == "head_style_prev" then
                    print("Cycling head style backward")
                    self:cycleAppearanceOption("head", nil, -1)
                    return true
                elseif id == "head_style_next" then
                    print("Cycling head style forward")
                    self:cycleAppearanceOption("head", nil, 1)
                    return true
                elseif id == "hair_style_prev" then
                    print("Cycling hair style backward")
                    self:cycleAppearanceOption("hair", "style", -1)
                    return true
                elseif id == "hair_style_next" then
                    print("Cycling hair style forward")
                    self:cycleAppearanceOption("hair", "style", 1)
                    return true
                elseif id == "hair_color_prev" then
                    print("Cycling hair color backward")
                    self:cycleAppearanceOption("hair", "color", -1)
                    return true
                elseif id == "hair_color_next" then
                    print("Cycling hair color forward")
                    self:cycleAppearanceOption("hair", "color", 1)
                    return true
                elseif id == "skin_tone_prev" then
                    print("Cycling skin tone backward")
                    self:cycleAppearanceOption("skin", "tone", -1)
                    return true
                elseif id == "skin_tone_next" then
                    print("Cycling skin tone forward")
                    self:cycleAppearanceOption("skin", "tone", 1)
                    return true
                elseif id == "eye_style_prev" then
                    print("Cycling eye style backward")
                    self:cycleAppearanceOption("eyes", "style", -1)
                    return true
                elseif id == "eye_style_next" then
                    print("Cycling eye style forward")
                    self:cycleAppearanceOption("eyes", "style", 1)
                    return true
                elseif id == "eye_color_prev" then
                    print("Cycling eye color backward")
                    self:cycleAppearanceOption("eyes", "color", -1)
                    return true
                elseif id == "eye_color_next" then
                    print("Cycling eye color forward")
                    self:cycleAppearanceOption("eyes", "color", 1)
                    return true
                end
            end
        end

        -- If we got here, no appearance button was clicked
        print("No appearance button was clicked at " .. x .. ", " .. y)
    end

    -- Handle tab-specific field clicks
    if form.fields then
        -- Handle field clicks based on current tab
        for fieldName, field in pairs(form.fields) do
            -- Only process fields for the current tab
            if field.tab == self.characterCreation.currentTab and
               x >= field.x and x <= field.x + field.width and
               y >= field.y and y <= field.y + field.height then

                print("Character creator field clicked: " .. fieldName)

                -- Handle different field types
                if fieldName == "name" then
                    -- Focus name field for text input
                    self.characterCreation.activeTextField = "name"

                    -- Activate text input mode in LÖVE
                    love.keyboard.setTextInput(true, field.x, field.y, field.width, field.height)

                    -- Set cursor position to end of text
                    self.characterCreation.cursorPos = utf8.len(field.value or "")

                    return true
                elseif fieldName == "gender" then
                    -- Toggle gender
                    if field.value == "male" then
                        field.value = "female"
                    else
                        field.value = "male"
                    end
                    self.characterCreation.data.gender = field.value
                    return true
                elseif fieldName == "class" then
                    -- Cycle through available classes
                    local classes = Engine.systems.characterCreator.getAvailableClasses() or {}
                    local nextClass = (self.characterCreation.data.class % #classes) + 1
                    self.characterCreation.data.class = nextClass
                    field.value = nextClass
                    self:updateClassPreview()
                    return true
                elseif fieldName == "kit" then
                    -- Cycle through available kits
                    local kits = {}
                    for name, _ in pairs(Engine.systems.characterCreator.startingKits) do
                        table.insert(kits, name)
                    end
                    local nextKit = (self.characterCreation.data.kit % #kits) + 1
                    self.characterCreation.data.kit = nextKit
                    field.value = nextKit
                    self:updateKitPreview()
                    return true
                end
            end
        end
    end

    -- Handle tab-specific content clicks
    if self.characterCreation.currentTab == "skills" then
        -- Handle skill selection clicks
        local skillsX = form.x + 80
        local skillsY = form.y + 240
        local skillWidth = 500
        local skillHeight = 50
        local skillSpacing = 15

        -- Get class information
        local classes = Engine.systems.characterCreator.getAvailableClasses() or {}
        local selectedClass = classes[self.characterCreation.data.class] or "Unknown"
        local skills = self:loadClassSkills(selectedClass)

        -- Check if clicked on a skill or its button
        for i, skill in ipairs(skills) do
            if i <= 5 then -- Match the display limit
                local y = skillsY + 20 + (i-1) * (skillHeight + skillSpacing)

                -- Check if clicked on the skill's select/deselect button
                local buttonX = skillsX + skillWidth - 100
                local buttonY = y + 10
                local buttonWidth = 70
                local buttonHeight = 30

                if x >= buttonX and x <= buttonX + buttonWidth and
                   y >= buttonY and y <= buttonY + buttonHeight then
                    self:toggleSkill(skill)
                    return true
                end

                -- Check if clicked on the skill itself
                if x >= skillsX + 15 and x <= skillsX + skillWidth - 30 and
                   y >= y and y <= y + skillHeight then
                    -- Show skill details or tooltip
                    return true
                end
            end
        end
    elseif self.characterCreation.currentTab == "equipment" then
        -- Handle shop clicks for Custom kit
        local kits = {}
        for name, _ in pairs(Engine.systems.characterCreator.startingKits) do
            table.insert(kits, name)
        end
        local selectedKit = kits[self.characterCreation.data.kit]

        if selectedKit == "Custom" and self.shopList then
            local shopX = form.x + form.width - 400
            local shopY = form.y + 180
            local shopWidth = 300
            local itemHeight = 45
            local itemSpacing = 10

            -- Check if clicked on a shop item
            local itemY = shopY + 120
            for i, entry in ipairs(self.shopList) do
                if i <= 5 then -- Match the display limit
                    if x >= shopX + 20 and x <= shopX + shopWidth - 20 and
                       y >= itemY and y <= itemY + itemHeight then
                        -- Buy item if enough gold
                        if self.characterCreation.preview.shopGold >= entry.cost then
                            self.characterCreation.preview.shopGold = self.characterCreation.preview.shopGold - entry.cost
                            self.characterCreation.preview.cart[entry.id] = (self.characterCreation.preview.cart[entry.id] or 0) + 1
                        end
                        return true
                    end
                    itemY = itemY + itemHeight + itemSpacing
                end
            end
        end
    end

    return false
end

-- Utility function to check if point is in rectangle
function UISystem:isPointInRect(x, y, rect)
    return x >= rect.x and x <= rect.x + rect.width and
           y >= rect.y and y <= rect.y + rect.height
end

-- Start character creation
function UISystem:startCharacterCreation()
    self.characterCreation.active = true
    self.characterCreation.currentTab = "basic" -- Start with the basic tab
    self.characterCreation.activeTextField = nil -- No text field active initially
    self.characterCreation.cursorPos = nil -- No cursor position initially

    -- Make sure text input is disabled initially (will be enabled when field is clicked)
    love.keyboard.setTextInput(false)

    -- Reset character creation data with defaults
    self.characterCreation.data = {
        name = "Player", -- Use placeholder initially
        class = 1,
        kit = 1,
        gender = "male",
        selectedSkills = {},
        appearance = {
            head = 1, -- Standard head style
            hair = {
                style = "short",
                color = "brown"
            },
            skin = {
                tone = "medium"
            },
            eyes = {
                style = "standard",
                color = "brown"
            },
            mouth = 1, -- Standard mouth
            body = {
                type = "average" -- Standard body type
            },
            outfit = 1, -- Standard outfit
            effect = nil -- No special effect
        }
    }

    -- Reset preview
    self.characterCreation.preview = {
        stats = {},
        equipment = {},
        skills = {},
        skillPoints = 3
    }

    -- Initialize preview stats and equipment
    self:updateClassPreview()
    self:updateKitPreview()

    -- If no slot to fill is specified, use the first empty slot
    if not self.characterCreation.slotToFill then
        for i, slot in ipairs(self.mainMenu.playerSlots) do
            if slot.empty then
                self.characterCreation.slotToFill = i
                break
            end
        end
    end

    -- If all slots are full and none was selected, use slot 1
    if not self.characterCreation.slotToFill then
        self.characterCreation.slotToFill = 1
    end

    -- For Custom kit, initialize shop data
    local kits = {}
    for name, _ in pairs(Engine.systems.characterCreator.startingKits) do
        table.insert(kits, name)
    end
    local selectedName = kits[self.characterCreation.data.kit]
    if selectedName == "Custom" then
        local kitInfo = Engine.systems.characterCreator.startingKits.Custom
        self.characterCreation.preview.shopGold = kitInfo.starting_gold or 0
        self.characterCreation.preview.cart = {}
    end

    -- Reset tab button hover states
    for _, tabName in ipairs(self.characterCreation.tabs) do
        local button = self.characterCreation.buttons[tabName]
        if button then
            button.hovered = false
        end
    end
end

-- Cancel character creation
function UISystem:cancelCharacterCreation()
    print("cancelCharacterCreation called - using direct handler")
    self:handleBackButtonClick()
end

-- Create character
function UISystem:createCharacter()
    -- Create the character using the Character Creator system
    local characterData = Engine.systems.characterCreator.createCharacter(
        self.characterCreation.data.name,
        self.characterCreation.data.class,
        self.characterCreation.data.kit
    )

    -- Add the character to the specified slot
    local slotIndex = self.characterCreation.slotToFill or 1
    local slot = self.mainMenu.playerSlots[slotIndex]

    if slot then
        slot.empty = false
        slot.name = self.characterCreation.data.name
        slot.level = 1
        slot.class = Engine.systems.characterCreator.getAvailableClasses()[self.characterCreation.data.kit]
        slot.character = characterData

        -- Apply appearance data to character
        if characterData and characterData.character then
            -- Store appearance details
            characterData.character.appearanceDetails = {
                hair = {
                    style = self.characterCreation.data.appearance.hair.style,
                    color = self.characterCreation.data.appearance.hair.color
                },
                skin = {
                    tone = self.characterCreation.data.appearance.skin.tone
                },
                eyes = {
                    color = self.characterCreation.data.appearance.eyes.color
                },
                body = {
                    type = self.characterCreation.data.appearance.body.type
                },
                gender = self.characterCreation.data.gender
            }

            -- Debug output
            print("Applied appearance data to character:")
            print("  Hair Style: " .. self.characterCreation.data.appearance.hair.style)
            print("  Hair Color: " .. self.characterCreation.data.appearance.hair.color)
            print("  Skin Tone: " .. self.characterCreation.data.appearance.skin.tone)
            print("  Eye Color: " .. self.characterCreation.data.appearance.eyes.color)
            print("  Body Type: " .. self.characterCreation.data.appearance.body.type)
            print("  Gender: " .. self.characterCreation.data.gender)
        end

        -- Save character to database if available
        if Engine.systems.database and Engine.systems.database.saveCharacter then
            print("Saving new character to database: " .. slot.name)
            Engine.systems.database.saveCharacter(characterData)
        end

        -- Select this slot
        for i, otherSlot in ipairs(self.mainMenu.playerSlots) do
            otherSlot.selected = (i == slotIndex)
        end

        -- For Custom kit, apply purchased items and leftover gold
        local kits = {}
        for name,_ in pairs(Engine.systems.characterCreator.startingKits) do kits[#kits+1] = name end
        local selectedKit = kits[self.characterCreation.data.kit]
        if selectedKit == "Custom" then
            slot.character.character.inventory.items = {}
            for id,count in pairs(self.characterCreation.preview.cart or {}) do
                local data = Engine.systems.characterCreator.startingKits.Custom.available_items[id]
                if data then
                    table.insert(slot.character.character.inventory.items, {id=id,name=data.name,count=count})
                end
            end
            slot.character.character.gold = self.characterCreation.preview.shopGold or 0
        end
    end

    -- Exit character creation
    self.characterCreation.active = false
    self.characterCreation.slotToFill = nil
    self.characterCreation.activeTextField = nil
    self.characterCreation.cursorPos = nil

    -- Make sure text input is disabled
    love.keyboard.setTextInput(false)

    -- Show success message
    self:addMessage("Character Created!", 2)
end

-- Show popup
function UISystem:showPopup(type, title, items)
    self.popup.active = true
    self.popup.type = type
    self.popup.title = title
    self.popup.items = items
    self.popup.selectedItem = 1
end

-- Hide popup
function UISystem:hidePopup()
    self.popup.active = false
end

-- Update class preview information
function UISystem:updateClassPreview()
    local classes = Engine.systems.characterCreator.getAvailableClasses()
    local selectedClass = classes[self.characterCreation.data.class]
    if selectedClass then
        local classInfo = {}
        if Engine.systems.characterCreator.getClassInfo then
            classInfo = Engine.systems.characterCreator.getClassInfo(selectedClass) or {}
        end
        if classInfo then
            self.characterCreation.preview.stats = {
                hp = classInfo.startingHP,
                mp = classInfo.startingMP,
                primaryStat = classInfo.primaryStat
            }
            self.characterCreation.preview.skills = classInfo.startingSkills or {}
        end
    end
end

-- Update kit preview information
function UISystem:updateKitPreview()
    local kits = {}
    for kitName, _ in pairs(Engine.systems.characterCreator.startingKits) do
        table.insert(kits, kitName)
    end
    local selectedKit = kits[self.characterCreation.data.kit]
    if selectedKit then
        local kitInfo = Engine.systems.characterCreator.startingKits[selectedKit]
        if kitInfo then
            -- Use items for standard kits and available_items for custom kits
            if kitInfo.is_custom and kitInfo.available_items then
                self.characterCreation.preview.equipment = kitInfo.available_items
            else
                self.characterCreation.preview.equipment = kitInfo.items or {}
            end
        end
    end
end

-- Draw skill tooltip
function UISystem:drawSkillTooltip(skill, x, y)
    love.graphics.setColor(0.1, 0.1, 0.1, 0.9)
    love.graphics.rectangle("fill", x, y, 300, 100)

    love.graphics.setColor(1, 1, 1)
    -- Safe description
    local desc = skill.description or ""
    love.graphics.print(desc, x + 10, y + 10)

    -- Draw skill effects
    if type(skill.effects) == "table" and #skill.effects > 0 then
        love.graphics.print("Effects:", x + 10, y + 40)
        for i, effect in ipairs(skill.effects) do
            -- Safe effect text
            local effText = effect or ""
            love.graphics.print("- " .. effText, x + 20, y + 60 + (i-1) * 20)
        end
    end
end

-- Check if mouse is over a skill
function UISystem:isMouseOverSkill(x, y, width, height)
    local mx, my = love.mouse.getPosition()
    return mx >= x and mx <= x + width and
           my >= y and my <= y + height
end

-- Toggle skill selection
function UISystem:toggleSkill(skill)
    if self.characterCreation.data.selectedSkills[skill.id] then
        -- Deselect skill
        self.characterCreation.data.selectedSkills[skill.id] = nil
        self.characterCreation.preview.skillPoints = self.characterCreation.preview.skillPoints + skill.cost
    else
        -- Check if we have enough points
        if self.characterCreation.preview.skillPoints >= skill.cost then
            self.characterCreation.data.selectedSkills[skill.id] = true
            self.characterCreation.preview.skillPoints = self.characterCreation.preview.skillPoints - skill.cost
        end
    end
end

-- Validate character creation
function UISystem:validateCharacterCreation()
    -- Check if name is valid
    if self.characterCreation.data.name == "" or self.characterCreation.data.name == "Player1" then
        self:showPopup("error", "Invalid Name", {"Please enter a valid character name"})
        return false
    end

    -- Check if skills are selected
    if self.characterCreation.preview.skillPoints > 0 then
        self:showPopup("warning", "Unused Skill Points", {"You have " .. self.characterCreation.preview.skillPoints .. " skill points remaining"})
        return false
    end

    return true
end

-- Load available skills for a class
function UISystem:loadClassSkills(className)
    local skills = {}
    local skillFiles = love.filesystem.getDirectoryItems("skills")

    for _, file in ipairs(skillFiles) do
        if file:match("%.lua$") then
            local skillName = file:gsub("%.lua$", "")
            local success, skillModule = pcall(require, "skills." .. skillName)

            if success and type(skillModule) == "table" then
                -- Check if skill is available for this class
                if not skillModule.requirements or
                   not skillModule.requirements.classes or
                   table.contains(skillModule.requirements.classes, className) then
                    table.insert(skills, {
                        id = skillModule.id,
                        name = skillModule.name,
                        description = skillModule.description,
                        cost = skillModule.requirements and skillModule.requirements.level or 1,
                        effects = {
                            "Damage: " .. (skillModule.properties and skillModule.properties.damage or "N/A"),
                            "Mana Cost: " .. (skillModule.properties and skillModule.properties.manaCost or "N/A"),
                            "Cooldown: " .. (skillModule.properties and skillModule.properties.cooldown or "N/A")
                        }
                    })
                end
            end
        end
    end

    return skills
end

-- In-Game Menu System
-- NOTE: Debug functionality should ONLY be in the F1 menu, not in the ESC menu
-- as the ESC menu will be accessible in the shipped game.
function UISystem:initInGameMenu()
    self.inGameMenu = {
        active = false,
        currentTab = "inventory",
        tabs = {"inventory", "equipment", "stats", "skills", "quests", "options"},
        selectedItem = nil,
        player = nil,
        buttons = {}
    }

    -- Initialize in-game menu buttons
    local buttonWidth = 160
    local buttonHeight = 40
    local buttonSpacing = 10

    self.inGameMenu.buttons = {
        resume = {
            text = "Resume Game",
            x = self.screenWidth - buttonWidth - 20,
            y = self.screenHeight - buttonHeight - 20,
            width = buttonWidth,
            height = buttonHeight,
            hovered = false,
            color = {0.2, 0.5, 0.8},
            hoverColor = {0.3, 0.6, 0.9},
            textColor = {1, 1, 1},
            action = function() self:closeCharacterScreen() end
        },
        exit = {
            text = "Exit to Menu",
            x = 20,
            y = self.screenHeight - buttonHeight - 20,
            width = buttonWidth,
            height = buttonHeight,
            hovered = false,
            color = {0.7, 0.2, 0.2},
            hoverColor = {0.9, 0.3, 0.3},
            textColor = {1, 1, 1},
            action = function()
                self:closeCharacterScreen()
                -- Change to menu state via stateManager
                local sm = Engine.stateManager
                if sm and sm.changeState then
                    sm:changeState("menu")
                end
                Engine.playerMenuVisible = false
            end
        },
        quit = {
            text = "Quit Game",
            x = (self.screenWidth - buttonWidth) / 2,
            y = self.screenHeight - buttonHeight - 20,
            width = buttonWidth,
            height = buttonHeight,
            hovered = false,
            color = {0.7, 0.2, 0.2},
            hoverColor = {0.9, 0.3, 0.3},
            textColor = {1,1,1},
            action = function() love.event.quit() end
        }
    }

    -- Initialize tab buttons
    local tabWidth = 120
    local tabX = 30

    for i, tabName in ipairs(self.inGameMenu.tabs) do
        self.inGameMenu.buttons[tabName] = {
            text = tabName:sub(1,1):upper() .. tabName:sub(2),
            x = tabX + (i-1) * (tabWidth + 10),
            y = 80,
            width = tabWidth,
            height = 35,
            hovered = false,
            color = {0.2, 0.3, 0.4},
            hoverColor = {0.3, 0.4, 0.5},
            textColor = {1, 1, 1},
            action = function() self.inGameMenu.currentTab = tabName end
        }
    end
end

-- Open the character screen / in-game menu
function UISystem:openCharacterScreen(player)
    -- Initialize in-game menu if not already done
    if not self.inGameMenu then
        self:initInGameMenu()
    end

    -- Activate the in-game menu
    self.inGameMenu.active = true
    self.inGameMenu.player = player

    -- Add a message
    self:addMessage("Character Screen Opened", 2)

    -- Pause game
    self.mainMenuActive = true
end

-- Close the character screen
function UISystem:closeCharacterScreen()
    if self.inGameMenu then
        self.inGameMenu.active = false
    end

    -- Resume game
    self.mainMenuActive = false

    -- Add a message
    self:addMessage("Resumed Game", 2)
end

-- Draw the in-game menu
function UISystem:drawInGameMenu()
    if not self.inGameMenu or not self.inGameMenu.active then return end

    local player = self.inGameMenu.player
    if not player then return end

    -- Draw semi-transparent background
    love.graphics.setColor(0.1, 0.1, 0.2, 0.9)
    love.graphics.rectangle("fill", 0, 0, self.screenWidth, self.screenHeight)

    -- Draw title
    love.graphics.setFont(self.titleFont)
    love.graphics.setColor(0.8, 0.9, 1.0)
    love.graphics.print("Character Screen", 30, 20)

    -- Draw player name and level
    love.graphics.setFont(self.largeFont)
    love.graphics.setColor(1, 1, 1)
    local playerName = player.name or "Player"
    local playerLevel = player.character and player.character.level or 1
    love.graphics.print(playerName .. " (Level " .. playerLevel .. ")", 30, 60)

    -- Draw tab buttons
    for _, tabName in ipairs(self.inGameMenu.tabs) do
        local button = self.inGameMenu.buttons[tabName]

        -- Set color based on selection/hover state
        local buttonColor = button.color
        if self.inGameMenu.currentTab == tabName then
            buttonColor = button.hoverColor
        elseif button.hovered then
            buttonColor = button.hoverColor
        end

        -- Draw button
        love.graphics.setColor(buttonColor[1], buttonColor[2], buttonColor[3], 0.8)
        love.graphics.rectangle("fill", button.x, button.y, button.width, button.height, 5, 5)

        -- Draw button text
        love.graphics.setColor(button.textColor[1], button.textColor[2], button.textColor[3])
        local textWidth = self.largeFont:getWidth(button.text)
        love.graphics.print(button.text, button.x + (button.width - textWidth) / 2, button.y + 5)
    end

    -- Draw content based on current tab
    love.graphics.setColor(0.2, 0.2, 0.3, 0.8)
    love.graphics.rectangle("fill", 30, 120, self.screenWidth - 60, self.screenHeight - 200, 10, 10)

    -- NOTE: Debug tab has been removed from the in-game menu (ESC menu)
    -- Debug functionality is now ONLY available in the F1 debug menu
    if self.inGameMenu.currentTab == "inventory" then
        self:drawInventoryTab(player)
    elseif self.inGameMenu.currentTab == "equipment" then
        self:drawEquipmentTab(player)
    elseif self.inGameMenu.currentTab == "stats" then
        self:drawStatsTab(player)
    elseif self.inGameMenu.currentTab == "skills" then
        self:drawSkillsTab(player)
    elseif self.inGameMenu.currentTab == "quests" then
        self:drawQuestsTab(player)
    elseif self.inGameMenu.currentTab == "options" then
        self:drawOptionsTab()
    end

    -- Draw action buttons (Resume, Exit)
    for _, buttonName in ipairs({"resume", "exit", "quit"}) do
        local button = self.inGameMenu.buttons[buttonName]

        -- Set color based on hover state
        local buttonColor = button.color
        if button.hovered then
            buttonColor = button.hoverColor
        end

        -- Draw button
        love.graphics.setColor(buttonColor[1], buttonColor[2], buttonColor[3], 0.8)
        love.graphics.rectangle("fill", button.x, button.y, button.width, button.height, 5, 5)

        -- Draw button text
        love.graphics.setColor(button.textColor[1], button.textColor[2], button.textColor[3])
        local textWidth = self.largeFont:getWidth(button.text)
        love.graphics.print(button.text, button.x + (button.width - textWidth) / 2, button.y + 5)
    end
end

-- Draw inventory tab
function UISystem:drawInventoryTab(player)
    love.graphics.setFont(self.largeFont)
    love.graphics.setColor(0.9, 0.9, 0.9)
    love.graphics.print("Inventory", 50, 130)

    -- Get player inventory
    local charData = player.character or player
    local inventory = charData.inventory or {}
    local items = inventory.items or {}

    -- ❶ Inventory panel's safe top-left inside the window
    local PANEL_X, PANEL_Y = 50, 170

    -- ❷ Spacing constants for perfect grid layout
    local SLOT = 50          -- box size
    local GAP_X = 10         -- horizontal gap
    local GAP_Y = 10         -- vertical gap
    local LABEL_PAD = 4      -- space between box and text
    local ROWS = 4           -- number of rows
    local COLS = 8           -- number of columns

    -- Track the hovered slot for Z-layer highlight
    local hoveredSlot = nil
    local mx, my = love.mouse.getPosition()

    -- Function to convert grid position to pixel coordinates
    local function slotXY(r, c)
        local x = PANEL_X + c * (SLOT + GAP_X)
        local y = PANEL_Y + r * (SLOT + GAP_Y)
        return x, y
    end

    -- Function to draw a slot with its item
    local function drawSlot(x, y, item, index)
        -- Draw slot background
        love.graphics.setColor(0.3, 0.3, 0.4, 0.8)
        love.graphics.rectangle("fill", x, y, SLOT, SLOT, 4)

        -- Draw slot border
        love.graphics.setColor(0.5, 0.5, 0.6, 0.8)
        love.graphics.rectangle("line", x, y, SLOT, SLOT, 4)

        -- Check if this slot is being hovered
        if mx >= x and mx <= x + SLOT and
           my >= y and my <= y + SLOT then
            hoveredSlot = {
                x = x,
                y = y,
                item = item,
                index = index
            }
        end

        -- Draw item if exists
        if item then
            -- Draw item background based on rarity
            local rarityColors = {
                common = {0.7, 0.7, 0.7},
                uncommon = {0.2, 0.8, 0.2},
                rare = {0.2, 0.2, 0.8},
                epic = {0.8, 0.2, 0.8},
                legendary = {1.0, 0.6, 0.1}
            }
            local rarity = item.rarity or "common"
            local rarityColor = rarityColors[rarity] or rarityColors.common

            love.graphics.setColor(rarityColor[1], rarityColor[2], rarityColor[3], 0.7)
            love.graphics.rectangle("fill", x + 4, y + 4, SLOT - 8, SLOT - 8, 2)

            -- Draw item icon (placeholder)
            love.graphics.setColor(1, 1, 1)
            love.graphics.rectangle("fill", x + 10, y + 10, SLOT - 20, SLOT - 20)

            -- Draw item count if stackable
            if item.count and item.count > 1 then
                love.graphics.setFont(self.font)
                love.graphics.setColor(1, 1, 1)
                love.graphics.print(item.count, x + SLOT - 15, y + SLOT - 20)
            end
        end
    end

    -- First pass: Draw all inventory slots
    for i = 1, ROWS * COLS do
        local row = math.floor((i-1) / COLS)
        local col = (i-1) % COLS
        local x, y = slotXY(row, col)

        -- Draw the slot with its item
        local item = items[i]
        drawSlot(x, y, item, i)
    end

    -- Draw item details panel if an item is selected
    if self.inGameMenu.selectedItem then
        local item = self.inGameMenu.selectedItem
        local detailsX = PANEL_X + (COLS + 0.5) * (SLOT + GAP_X)
        local detailsY = PANEL_Y
        local detailsWidth = 300
        local detailsHeight = 300

        -- Draw details panel background
        love.graphics.setColor(0.2, 0.2, 0.3, 0.8)
        love.graphics.rectangle("fill", detailsX, detailsY, detailsWidth, detailsHeight, 10, 10)

        -- Draw panel title
        love.graphics.setFont(self.largeFont)
        love.graphics.setColor(1, 0.8, 0.2)
        love.graphics.print("Selected Item", detailsX + 20, detailsY + 10)

        -- Draw item name
        love.graphics.setFont(self.largeFont)
        love.graphics.setColor(1, 1, 1)
        love.graphics.print(item.name or "Unknown Item", detailsX + 20, detailsY + 40)

        -- Draw item details
        love.graphics.setFont(self.font)
        love.graphics.setColor(0.8, 0.8, 0.8)
        love.graphics.print("Type: " .. (item.type or "Unknown"), detailsX + 20, detailsY + 80)
        love.graphics.print("Value: " .. (item.value or 0) .. " gold", detailsX + 20, detailsY + 100)

        if item.description then
            love.graphics.print("Description:", detailsX + 20, detailsY + 130)
            love.graphics.setColor(0.9, 0.9, 0.9)
            love.graphics.printf(item.description, detailsX + 20, detailsY + 150, detailsWidth - 40, "left")
        end

        -- Check if this is an equipment item
        local isEquipment = false
        if item.id then
            local itemData = nil
            if Engine.systems.itemDatabase and Engine.systems.itemDatabase.getItem then
                itemData = Engine.systems.itemDatabase.getItem(item.id)
            elseif Engine.systems.equipment and Engine.systems.equipment.load then
                itemData = Engine.systems.equipment.load(item.id)
            end

            if itemData and itemData.equipmentType then
                isEquipment = true

                -- Draw equip button
                local btnX = detailsX + (detailsWidth - 120) / 2  -- Center the button
                local btnY = detailsY + detailsHeight - 50
                local btnWidth = 120
                local btnHeight = 30

                love.graphics.setColor(0.2, 0.6, 0.2, 0.8)
                love.graphics.rectangle("fill", btnX, btnY, btnWidth, btnHeight, 8, 8)

                love.graphics.setColor(1, 1, 1)
                love.graphics.print("Equip Item", btnX + 20, btnY + 5)

                -- Store button data for click handling
                self.inGameMenu.equipButton = {
                    x = btnX,
                    y = btnY,
                    width = btnWidth,
                    height = btnHeight,
                    itemId = item.id,
                    itemData = itemData
                }
            else
                self.inGameMenu.equipButton = nil
            end
        else
            self.inGameMenu.equipButton = nil
        end
    end

    -- Draw hover highlight on top of everything else
    if hoveredSlot then
        -- Draw a glowing highlight around the hovered slot
        love.graphics.setColor(1, 0.8, 0.2, 0.5)  -- Golden glow
        love.graphics.setLineWidth(2)
        love.graphics.rectangle("line",
            hoveredSlot.x - 2,
            hoveredSlot.y - 2,
            SLOT + 4,
            SLOT + 4,
            5, 5)
        love.graphics.setLineWidth(1)

        -- Show tooltip for the hovered item
        if hoveredSlot.item then
            local tooltipX = love.mouse.getX() + 6
            local tooltipY = love.mouse.getY() + 10
            local tooltipWidth = 150
            local tooltipHeight = 40

            -- Keep tooltip on screen
            if tooltipX + tooltipWidth > love.graphics.getWidth() then
                tooltipX = love.graphics.getWidth() - tooltipWidth - 10
            end
            if tooltipY + tooltipHeight > love.graphics.getHeight() then
                tooltipY = love.graphics.getHeight() - tooltipHeight - 10
            end

            love.graphics.setColor(0.2, 0.2, 0.3, 0.9)
            love.graphics.rectangle("fill", tooltipX, tooltipY, tooltipWidth, tooltipHeight, 5, 5)

            love.graphics.setColor(1, 1, 1)
            love.graphics.print(hoveredSlot.item.name or "Unknown Item", tooltipX + 10, tooltipY + 10)
        end
    end
end

-- Draw stats tab
function UISystem:drawStatsTab(player)
    love.graphics.setFont(self.largeFont)
    love.graphics.setColor(0.9, 0.9, 0.9)
    love.graphics.print("Character Stats", 50, 130)

    -- Get character stats
    local charData = player.character or player
    local stats = charData.stats or {
        strength = 10,
        dexterity = 10,
        intelligence = 10,
        vitality = 10,
        luck = 10
    }

    local derivedStats = charData.derivedStats or {
        maxHP = 100,
        currentHP = 100,
        maxMP = 50,
        currentMP = 50,
        defense = 5,
        magicDefense = 5,
        accuracy = 80,
        evasion = 10,
        critRate = 5
    }

    -- Draw primary stats
    love.graphics.setFont(self.largeFont)
    love.graphics.setColor(0.9, 0.9, 0.9)
    love.graphics.print("Primary Stats", 50, 170)

    local statY = 200
    local statSpacing = 30
    local statNames = {"Strength", "Dexterity", "Intelligence", "Vitality", "Luck"}
    local statKeys = {"strength", "dexterity", "intelligence", "vitality", "luck"}

    for i, name in ipairs(statNames) do
        local key = statKeys[i]
        local value = stats[key] or 0

        love.graphics.setColor(0.8, 0.8, 0.8)
        love.graphics.print(name .. ":", 50, statY + (i-1) * statSpacing)

        love.graphics.setColor(1, 1, 1)
        love.graphics.print(value, 200, statY + (i-1) * statSpacing)

        -- Draw stat bar
        love.graphics.setColor(0.3, 0.3, 0.4)
        love.graphics.rectangle("fill", 250, statY + (i-1) * statSpacing + 10, 200, 10)

        love.graphics.setColor(0.4, 0.6, 0.9)
        local barLength = math.min(200, (value / 50) * 200)
        love.graphics.rectangle("fill", 250, statY + (i-1) * statSpacing + 10, barLength, 10)
    end

    -- Draw health and mana bars
    love.graphics.setFont(self.largeFont)
    love.graphics.setColor(0.9, 0.9, 0.9)
    love.graphics.print("Health & Mana", 500, 170)

    -- Health bar
    love.graphics.setColor(0.3, 0.3, 0.4)
    love.graphics.rectangle("fill", 500, 200, 250, 20)

    local healthPercent = derivedStats.currentHP / derivedStats.maxHP
    love.graphics.setColor(0.9, 0.2, 0.2)
    love.graphics.rectangle("fill", 500, 200, 250 * healthPercent, 20)

    love.graphics.setColor(1, 1, 1)
    love.graphics.print("HP: " .. derivedStats.currentHP .. " / " .. derivedStats.maxHP, 510, 201)

    -- Mana bar
    love.graphics.setColor(0.3, 0.3, 0.4)
    love.graphics.rectangle("fill", 500, 230, 250, 20)

    local manaPercent = derivedStats.currentMP / derivedStats.maxMP
    love.graphics.setColor(0.2, 0.4, 0.9)
    love.graphics.rectangle("fill", 500, 230, 250 * manaPercent, 20)

    love.graphics.setColor(1, 1, 1)
    love.graphics.print("MP: " .. derivedStats.currentMP .. " / " .. derivedStats.maxMP, 510, 231)

    -- Draw derived stats
    love.graphics.setFont(self.largeFont)
    love.graphics.setColor(0.9, 0.9, 0.9)
    love.graphics.print("Combat Stats", 500, 280)

    local derivedY = 310
    local derivedStats = {
        {"Defense", derivedStats.defense},
        {"Magic Defense", derivedStats.magicDefense},
        {"Accuracy", derivedStats.accuracy .. "%"},
        {"Evasion", derivedStats.evasion .. "%"},
        {"Critical Rate", derivedStats.critRate .. "%"}
    }

    for i, stat in ipairs(derivedStats) do
        love.graphics.setColor(0.8, 0.8, 0.8)
        love.graphics.print(stat[1] .. ":", 500, derivedY + (i-1) * statSpacing)

        love.graphics.setColor(1, 1, 1)
        love.graphics.print(stat[2], 650, derivedY + (i-1) * statSpacing)
    end
end

-- Draw skills tab
function UISystem:drawSkillsTab(player)
    love.graphics.setFont(self.largeFont)
    love.graphics.setColor(0.9, 0.9, 0.9)
    love.graphics.print("Skills", 50, 130)

    -- Get player skills
    local charData = player.character or player
    local skills = charData.skills or {}

    -- If no skills, show message
    if #skills == 0 then
        love.graphics.setFont(self.font)
        love.graphics.setColor(0.8, 0.8, 0.8)
        love.graphics.print("No skills learned yet.", 50, 170)
        return
    end

    -- Draw skill list
    local skillY = 170
    local skillSpacing = 40

    for i, skill in ipairs(skills) do
        local y = skillY + (i-1) * skillSpacing

        -- Draw skill background
        if i % 2 == 1 then
            love.graphics.setColor(0.25, 0.25, 0.35, 0.5)
        else
            love.graphics.setColor(0.3, 0.3, 0.4, 0.5)
        end
        love.graphics.rectangle("fill", 50, y, self.screenWidth - 120, 35)

        -- Draw skill name
        love.graphics.setFont(self.font)
        love.graphics.setColor(0.9, 0.9, 0.9)
        love.graphics.print(skill.name, 60, y + 10)

        -- Draw skill type/element
        if skill.element then
            local elementColors = {
                fire = {0.9, 0.3, 0.1},
                ice = {0.2, 0.8, 0.9},
                lightning = {0.9, 0.8, 0.1},
                earth = {0.6, 0.4, 0.1},
                wind = {0.6, 0.9, 0.6},
                light = {0.9, 0.9, 0.6},
                dark = {0.4, 0.1, 0.4}
            }

            local color = elementColors[skill.element] or {0.7, 0.7, 0.7}
            love.graphics.setColor(color[1], color[2], color[3])
            love.graphics.print(skill.element:upper(), 300, y + 10)
        end

        -- Draw skill cost
        if skill.mpCost then
            love.graphics.setColor(0.4, 0.6, 0.9)
            love.graphics.print("MP: " .. skill.mpCost, 400, y + 10)
        end

        -- Draw skill cooldown
        if skill.cooldown then
            love.graphics.setColor(0.9, 0.6, 0.3)
            love.graphics.print("CD: " .. skill.cooldown .. "s", 500, y + 10)
        end
    end
end

-- Draw quests tab
function UISystem:drawQuestsTab(player)
    love.graphics.setFont(self.largeFont)
    love.graphics.setColor(0.9, 0.9, 0.9)
    love.graphics.print("Quests", 50, 130)

    -- Get player quests
    local charData = player.character or player
    local quests = charData.quests or {}
    local activeQuests = quests.active or {}
    local completedQuests = quests.completed or {}

    -- If no quests, show message
    if #activeQuests == 0 and #completedQuests == 0 then
        love.graphics.setFont(self.font)
        love.graphics.setColor(0.8, 0.8, 0.8)
        love.graphics.print("No active quests.", 50, 170)
        return
    end

    -- Draw active quests
    love.graphics.setFont(self.largeFont)
    love.graphics.setColor(0.9, 0.9, 0.9)
    love.graphics.print("Active Quests", 50, 170)

    local questY = 210
    local questSpacing = 70

    for i, quest in ipairs(activeQuests) do
        local y = questY + (i-1) * questSpacing

        -- Draw quest background
        love.graphics.setColor(0.3, 0.3, 0.4, 0.7)
        love.graphics.rectangle("fill", 50, y, self.screenWidth - 120, 60, 5, 5)

        -- Draw quest name
        love.graphics.setFont(self.largeFont)
        love.graphics.setColor(1, 1, 1)
        love.graphics.print(quest.name, 60, y + 5)

        -- Draw quest description
        love.graphics.setFont(self.font)
        love.graphics.setColor(0.8, 0.8, 0.8)
        love.graphics.print(quest.description, 60, y + 35)

        -- Draw quest progress
        if quest.progress and quest.target then
            love.graphics.setColor(0.3, 0.5, 0.9)
            love.graphics.print("Progress: " .. quest.progress .. " / " .. quest.target, self.screenWidth - 220, y + 10)
        end
    end

    -- Draw completed quests
    local completedY = questY + #activeQuests * questSpacing + 30

    if #completedQuests > 0 then
        love.graphics.setFont(self.largeFont)
        love.graphics.setColor(0.9, 0.9, 0.9)
        love.graphics.print("Completed Quests", 50, completedY)

        for i, quest in ipairs(completedQuests) do
            local y = completedY + 40 + (i-1) * 30

            -- Draw quest name (simple list for completed quests)
            love.graphics.setFont(self.font)
            love.graphics.setColor(0.7, 0.9, 0.7)
            love.graphics.print("✓ " .. quest.name, 60, y)
        end
    end
end

-- Draw equipment tab
function UISystem:drawEquipmentTab(player)
    love.graphics.setFont(self.largeFont)
    love.graphics.setColor(0.9, 0.9, 0.9)
    love.graphics.print("Equipment", 50, 130)

    -- Get character equipment
    local charData = player.character or player
    local equipment = charData.equipment or {}

    -- ❶ Equipment panel's safe top-left inside the window
    local PANEL_X, PANEL_Y = 80, 152  -- Adjusted to keep Head clear of the tab bar

    -- ❷ Spacing constants for perfect grid layout
    local SLOT = 64          -- box size
    local GAP_X = 12         -- horizontal gap
    local GAP_Y = 16         -- vertical gap
    local LABEL_PAD = 4      -- space between box and text
    local COLS_LEFT = -2     -- left-most column index

    -- Define the spine column for perfect vertical alignment
    local C_SPINE = 0  -- Centered spine column (same as Chest/Legs/Feet)

    -- Define slots in a fixed grid (r=row, c=column)
    -- Five columns (-2 to +2), five rows (0 to 4)
    local grid = {
        -- Top row - perfectly aligned for straight shoulders
        ammo        = { r = 0, c = -2 },
        head        = { r = 0, c = C_SPINE },  -- Aligned with spine column (Chest)
        accessory1  = { r = 0, c = C_SPINE + 2 },  -- Outer-right

        -- Second row - neck dropped half a slot for breathing room
        back        = { r = 1, c = -2 },
        neck        = { r = 1, c = C_SPINE },  -- Aligned with spine column (Chest)
        accessory2  = { r = 1, c = C_SPINE + 1 },  -- Inner-right

        -- Third row - equal arm span for perfect symmetry
        ranged      = { r = 2, c = -2 },  -- Leftmost arm position
        mainhand    = { r = 2, c = -1 },
        chest       = { r = 2, c = C_SPINE },  -- Aligned with spine column
        hands       = { r = 2, c = C_SPINE + 1 },
        offhand     = { r = 2, c = C_SPINE + 2 },  -- Rightmost arm position

        -- Fourth row - centered on the body
        legs        = { r = 3, c = C_SPINE },  -- Aligned with spine column

        -- Bottom row - symmetrical rings
        ring1       = { r = 4, c = C_SPINE - 1 },
        feet        = { r = 4, c = C_SPINE },  -- Aligned with spine column
        ring2       = { r = 4, c = C_SPINE + 1 }
    }

    -- Define shorter labels for slots to prevent overlap
    local labels = {
        mainhand = "Main",
        offhand = "Off",
        accessory1 = "Acc 1",
        accessory2 = "Acc 2",
        ring1 = "Ring 1",
        ring2 = "Ring 2"
    }

    -- Function to convert grid position to pixel coordinates
    local function slotXY(r, c)
        -- c == -2 .. +2  ;  r == 0 .. 4
        local x = PANEL_X + (c - COLS_LEFT) * (SLOT + GAP_X)
        local y = PANEL_Y + r * (SLOT + GAP_Y)
        return x, y
    end

    -- Create slots array with calculated positions
    local slots = {}
    for id, pos in pairs(grid) do
        local name = id:gsub("^%l", string.upper)  -- Capitalize first letter
        if id == "mainhand" then name = "Main Hand"
        elseif id == "offhand" then name = "Off Hand"
        elseif id == "ring1" then name = "Ring 1"
        elseif id == "ring2" then name = "Ring 2"
        elseif id == "accessory1" then name = "Accessory 1"
        elseif id == "accessory2" then name = "Accessory 2"
        end

        local x, y = slotXY(pos.r, pos.c)
        table.insert(slots, {
            id = id,
            name = name,
            label = labels[id] or name,
            x = x,
            y = y
        })
    end

    -- Store equipment slot hitboxes for click detection
    self.inGameMenu.equipmentSlots = {}

    -- Draw equipment background (mannequin shape)
    love.graphics.setColor(0.2, 0.2, 0.3, 0.3)

    -- Calculate the center point of the grid for drawing the mannequin
    local centerX, _ = slotXY(0, C_SPINE)  -- Head position (center column)
    local _, headY = slotXY(0, 0)          -- Row 0 (head row)
    local _, neckY = slotXY(1, 0)          -- Row 1 (neck row)
    local _, chestY = slotXY(2, 0)         -- Row 2 (chest row)
    local _, legsY = slotXY(3, 0)          -- Row 3 (legs row)
    local _, feetY = slotXY(4, 0)          -- Row 4 (feet row)

    -- Draw the outline of the mannequin shape
    local outlineWidth = (SLOT + GAP_X) * 5
    local outlineHeight = (SLOT + GAP_Y) * 5
    love.graphics.rectangle("fill", centerX - outlineWidth/2, headY - SLOT/2,
                           outlineWidth, outlineHeight, 15, 15)

    -- Draw a faint mannequin silhouette
    love.graphics.setColor(0.3, 0.3, 0.4, 0.15)

    -- Head (circle)
    love.graphics.circle("fill", centerX, headY + SLOT/2, SLOT * 0.4)

    -- Torso (trapezoid)
    love.graphics.polygon("fill",
        centerX - SLOT * 0.5, neckY + SLOT/2,     -- Shoulders top left
        centerX + SLOT * 0.5, neckY + SLOT/2,     -- Shoulders top right
        centerX + SLOT * 0.7, legsY,              -- Hips bottom right
        centerX - SLOT * 0.7, legsY               -- Hips bottom left
    )

    -- Arms
    local leftHandX, leftHandY = slotXY(2, -1)  -- Main Hand position
    local rightHandX, rightHandY = slotXY(2, 1) -- Hands position

    love.graphics.polygon("fill",
        centerX - SLOT * 0.5, neckY + SLOT * 0.7,  -- Left shoulder
        leftHandX + SLOT/2, leftHandY + SLOT/2,    -- Left hand
        leftHandX + SLOT * 0.6, leftHandY + SLOT * 0.6, -- Left wrist
        centerX - SLOT * 0.4, neckY + SLOT * 0.9   -- Left armpit
    )

    love.graphics.polygon("fill",
        centerX + SLOT * 0.5, neckY + SLOT * 0.7,  -- Right shoulder
        rightHandX + SLOT/2, rightHandY + SLOT/2,  -- Right hand
        rightHandX + SLOT * 0.4, rightHandY + SLOT * 0.6, -- Right wrist
        centerX + SLOT * 0.4, neckY + SLOT * 0.9   -- Right armpit
    )

    -- Legs
    love.graphics.polygon("fill",
        centerX - SLOT * 0.6, legsY,               -- Left hip
        centerX - SLOT * 0.2, legsY,               -- Inner left hip
        centerX - SLOT * 0.3, feetY,               -- Left foot
        centerX - SLOT * 0.7, feetY                -- Outer left foot
    )

    love.graphics.polygon("fill",
        centerX + SLOT * 0.6, legsY,               -- Right hip
        centerX + SLOT * 0.2, legsY,               -- Inner right hip
        centerX + SLOT * 0.3, feetY,               -- Right foot
        centerX + SLOT * 0.7, feetY                -- Outer right foot
    )

    -- Draw equipment slots
    local slotSize = SLOT  -- Use the defined SLOT size

    -- Track the hovered slot for Z-layer highlight
    local hoveredSlot = nil
    local mx, my = love.mouse.getPosition()

    -- Function to draw a slot with its label
    local function drawSlot(slot, equipped)
        -- Draw slot background
        love.graphics.setColor(0.3, 0.3, 0.4, 0.8)
        love.graphics.rectangle("fill", slot.x, slot.y, slotSize, slotSize, 4)

        -- Draw slot border
        love.graphics.setColor(0.5, 0.5, 0.6, 0.8)
        love.graphics.rectangle("line", slot.x, slot.y, slotSize, slotSize, 4)

        -- Draw label below the slot, centered within the column
        if not equipped then
            love.graphics.setFont(self.font)
            local labelText = slot.label
            local w = self.font:getWidth(labelText)
            local labelX = slot.x + (slotSize - w) / 2  -- Horizontal center
            local labelY = slot.y + slotSize + LABEL_PAD  -- Just under the box

            -- Set scissor to prevent label from bleeding into neighboring slots
            love.graphics.setScissor(slot.x, labelY, slotSize, self.font:getHeight())
            love.graphics.setColor(0.8, 0.8, 0.8, 1.0)
            love.graphics.print(labelText, labelX, labelY)
            love.graphics.setScissor()  -- Turn clipping off
        end
    end

    -- First pass: Draw all slot backgrounds, outlines, and labels
    for _, slot in ipairs(slots) do
        -- Check if this slot is being hovered
        if mx >= slot.x and mx <= slot.x + slotSize and
           my >= slot.y and my <= slot.y + slotSize then
            hoveredSlot = slot
        end

        -- Draw the slot with its label
        local itemId = equipment[slot.id]
        drawSlot(slot, itemId)
    end

    -- Second pass: Draw equipped items
    for _, slot in ipairs(slots) do
        -- Draw equipped item if any
        local itemId = equipment[slot.id]
        if itemId then
            -- Get item data
            local itemData = nil
            if Engine.systems.itemDatabase and Engine.systems.itemDatabase.getItem then
                itemData = Engine.systems.itemDatabase.getItem(itemId)
            elseif Engine.systems.equipment and Engine.systems.equipment.load then
                itemData = Engine.systems.equipment.load(itemId)
            end

            if itemData then
                -- Draw item background based on rarity
                local rarityColors = {
                    common = {0.7, 0.7, 0.7},
                    uncommon = {0.2, 0.8, 0.2},
                    rare = {0.2, 0.2, 0.8},
                    epic = {0.8, 0.2, 0.8},
                    legendary = {1.0, 0.6, 0.1}
                }
                local rarity = itemData.rarity or "common"
                local rarityColor = rarityColors[rarity] or rarityColors.common

                love.graphics.setColor(rarityColor[1], rarityColor[2], rarityColor[3], 0.7)
                love.graphics.rectangle("fill", slot.x + slotPadding, slot.y + slotPadding,
                                       slotSize - 2 * slotPadding, slotSize - 2 * slotPadding)

                -- Draw item name
                love.graphics.setColor(1, 1, 1)
                local name = itemData.name or "Unknown Item"
                -- Truncate name if too long
                if self.font:getWidth(name) > slotSize - 10 then
                    local truncated = ""
                    for i = 1, #name do
                        local testStr = truncated .. name:sub(i, i)
                        if self.font:getWidth(testStr) > slotSize - 15 then
                            truncated = truncated .. "..."
                            break
                        end
                        truncated = testStr
                    end
                    name = truncated
                end
                love.graphics.printf(name, slot.x + 5, slot.y + slotSize/2 - 10, slotSize - 10, "center")
            else
                -- Draw placeholder for unknown item
                love.graphics.setColor(0.6, 0.6, 0.6, 0.5)
                love.graphics.rectangle("fill", slot.x + slotPadding, slot.y + slotPadding,
                                       slotSize - 2 * slotPadding, slotSize - 2 * slotPadding)

                love.graphics.setColor(1, 1, 1, 0.7)
                love.graphics.printf(itemId or "Unknown", slot.x + 5, slot.y + slotSize/2 - 10, slotSize - 10, "center")
            end
        end

        -- Store slot hitbox for click detection
        self.inGameMenu.equipmentSlots[slot.id] = {
            x = slot.x,
            y = slot.y,
            width = slotSize,
            height = slotSize,
            itemId = itemId
        }
    end

    -- Third pass: Draw hover highlight on top of everything else
    if hoveredSlot then
        -- Draw a glowing highlight around the hovered slot
        love.graphics.setColor(1, 0.8, 0.2, 0.5)  -- Golden glow
        love.graphics.setLineWidth(2)
        love.graphics.rectangle("line",
            hoveredSlot.x - 2,
            hoveredSlot.y - 2,
            slotSize + 4,
            slotSize + 4,
            5, 5)
        love.graphics.setLineWidth(1)

        -- Show tooltip for the hovered slot - position relative to mouse cursor
        local tooltipX = love.mouse.getX() + 6
        local tooltipY = love.mouse.getY() + 10
        local tooltipWidth = 150
        local tooltipHeight = 40

        -- Keep tooltip on screen
        if tooltipX + tooltipWidth > love.graphics.getWidth() then
            tooltipX = love.graphics.getWidth() - tooltipWidth - 10
        end
        if tooltipY + tooltipHeight > love.graphics.getHeight() then
            tooltipY = love.graphics.getHeight() - tooltipHeight - 10
        end

        love.graphics.setColor(0.2, 0.2, 0.3, 0.9)
        love.graphics.rectangle("fill", tooltipX, tooltipY, tooltipWidth, tooltipHeight, 5, 5)

        love.graphics.setColor(1, 1, 1)
        local itemId = equipment[hoveredSlot.id]
        if itemId then
            love.graphics.print("Unequip " .. hoveredSlot.name, tooltipX + 10, tooltipY + 10)
        else
            love.graphics.print("Equip to " .. hoveredSlot.name, tooltipX + 10, tooltipY + 10)
        end
    end

    -- Draw equipment stats panel on the right side
    local statsX = PANEL_X + 5 * (SLOT + GAP_X) + 20  -- Position to the right of the equipment grid
    local statsY = PANEL_Y
    local statsWidth = 300
    local statsHeight = 200

    -- Draw stats panel background
    love.graphics.setColor(0.2, 0.2, 0.3, 0.8)
    love.graphics.rectangle("fill", statsX, statsY, statsWidth, statsHeight, 10, 10)

    -- Draw panel title
    love.graphics.setFont(self.largeFont)
    love.graphics.setColor(0.9, 0.9, 0.9)
    love.graphics.print("Equipment Stats", statsX + 20, statsY + 20)

    -- Calculate equipment stats
    local equipStats = {
        pattack = 0,
        pdefense = 0,
        mattack = 0,
        mdefense = 0,
        health = 0,
        mana = 0,
        speed = 0,
        critChance = 0
    }

    -- If equipment system is available, use it to calculate stats
    if Engine.systems.equipment and Engine.systems.equipment.calculateStats then
        equipStats = Engine.systems.equipment.calculateStats(equipment) or equipStats
    else
        -- Simple fallback calculation
        for _, itemId in pairs(equipment) do
            local itemData = nil
            if Engine.systems.itemDatabase and Engine.systems.itemDatabase.getItem then
                itemData = Engine.systems.itemDatabase.getItem(itemId)
            end

            if itemData and itemData.stats then
                for stat, value in pairs(itemData.stats) do
                    if equipStats[stat] then
                        equipStats[stat] = equipStats[stat] + value
                    end
                end
            end
        end
    end

    -- Draw equipment stats in two columns
    local leftColX = statsX + 30
    local rightColX = statsX + 170
    local statsStartY = statsY + 60
    local statSpacing = 25

    love.graphics.setFont(self.font)
    love.graphics.setColor(0.8, 0.8, 0.8)

    -- Left column
    love.graphics.print("Physical Attack: " .. equipStats.pattack, leftColX, statsStartY)
    love.graphics.print("Physical Defense: " .. equipStats.pdefense, leftColX, statsStartY + statSpacing)
    love.graphics.print("Magical Attack: " .. equipStats.mattack, leftColX, statsStartY + 2 * statSpacing)
    love.graphics.print("Magical Defense: " .. equipStats.mdefense, leftColX, statsStartY + 3 * statSpacing)

    -- Right column
    love.graphics.print("Health: " .. equipStats.health, rightColX, statsStartY)
    love.graphics.print("Mana: " .. equipStats.mana, rightColX, statsStartY + statSpacing)
    love.graphics.print("Speed: " .. equipStats.speed, rightColX, statsStartY + 2 * statSpacing)
    love.graphics.print("Crit Chance: " .. equipStats.critChance .. "%", rightColX, statsStartY + 3 * statSpacing)

    -- Draw total equipment rating
    local totalRating = equipStats.pattack + equipStats.pdefense + equipStats.mattack + equipStats.mdefense
    love.graphics.setColor(1, 0.8, 0.2)
    love.graphics.print("Equipment Rating: " .. totalRating, statsX + 20, statsY + statsHeight - 40)

    -- Draw selected item details if any
    if self.inGameMenu.selectedEquipment then
        local detailsX = statsX  -- Align with stats panel
        local detailsY = statsY + statsHeight + 20
        local detailsWidth = 300
        local detailsHeight = 180

        -- Draw details background
        love.graphics.setColor(0.3, 0.3, 0.4, 0.9)
        love.graphics.rectangle("fill", detailsX, detailsY, detailsWidth, detailsHeight, 10, 10)

        -- Draw panel title
        love.graphics.setFont(self.largeFont)
        love.graphics.setColor(1, 0.8, 0.2)
        love.graphics.print("Selected Item", detailsX + 20, detailsY + 10)

        -- Get item data
        local itemId = self.inGameMenu.selectedEquipment
        local itemData = nil
        if Engine.systems.itemDatabase and Engine.systems.itemDatabase.getItem then
            itemData = Engine.systems.itemDatabase.getItem(itemId)
        elseif Engine.systems.equipment and Engine.systems.equipment.load then
            itemData = Engine.systems.equipment.load(itemId)
        end

        if itemData then
            -- Draw item name
            love.graphics.setFont(self.largeFont)
            love.graphics.setColor(1, 1, 1)
            love.graphics.print(itemData.name or "Unknown Item", detailsX + 20, detailsY + 40)

            -- Draw item type
            love.graphics.setFont(self.font)
            love.graphics.setColor(0.8, 0.8, 0.8)
            local typeStr = ""
            if itemData.equipmentType and itemData.equipmentSubType then
                if Engine.systems.equipment and Engine.systems.equipment.types and
                   Engine.systems.equipment.types[itemData.equipmentType] and
                   Engine.systems.equipment.types[itemData.equipmentType][itemData.equipmentSubType] then
                    typeStr = Engine.systems.equipment.types[itemData.equipmentType][itemData.equipmentSubType]
                else
                    typeStr = itemData.equipmentType .. " - " .. itemData.equipmentSubType
                end
            end
            love.graphics.print("Type: " .. typeStr, detailsX + 20, detailsY + 70)

            -- Draw item stats in a more compact format
            if itemData.stats then
                love.graphics.print("Stats:", detailsX + 20, detailsY + 95)

                -- Draw stats in two columns
                local leftColX = detailsX + 30
                local rightColX = detailsX + 160
                local statStartY = detailsY + 115
                local statSpacing = 20
                local statCount = 0

                for stat, value in pairs(itemData.stats) do
                    local statName = stat
                    if stat == "pattack" then statName = "P.Atk"
                    elseif stat == "pdefense" then statName = "P.Def"
                    elseif stat == "mattack" then statName = "M.Atk"
                    elseif stat == "mdefense" then statName = "M.Def"
                    elseif stat == "health" then statName = "HP"
                    elseif stat == "mana" then statName = "MP"
                    elseif stat == "speed" then statName = "Spd"
                    elseif stat == "critChance" then statName = "Crit"
                    end

                    local colX = (statCount % 2 == 0) and leftColX or rightColX
                    local rowY = statStartY + math.floor(statCount / 2) * statSpacing

                    love.graphics.print(statName .. ": " .. value, colX, rowY)
                    statCount = statCount + 1

                    -- Limit to 6 stats to avoid overflow
                    if statCount >= 6 then break end
                end
            end

            -- Draw unequip button
            local btnX = detailsX + (detailsWidth - 100) / 2  -- Center the button
            local btnY = detailsY + detailsHeight - 40
            local btnWidth = 100
            local btnHeight = 30

            love.graphics.setColor(0.7, 0.2, 0.2, 0.8)
            love.graphics.rectangle("fill", btnX, btnY, btnWidth, btnHeight, 8, 8)

            love.graphics.setColor(1, 1, 1)
            love.graphics.print("Unequip", btnX + 20, btnY + 5)

            -- Store button hitbox
            self.inGameMenu.unequipButton = {
                x = btnX,
                y = btnY,
                width = btnWidth,
                height = btnHeight,
                itemId = itemId
            }
        else
            -- Draw placeholder for unknown item
            love.graphics.setFont(self.largeFont)
            love.graphics.setColor(1, 1, 1)
            love.graphics.print("Unknown Item", detailsX + 20, detailsY + 40)

            love.graphics.setFont(self.font)
            love.graphics.setColor(0.8, 0.8, 0.8)
            love.graphics.print("This item's data could not be found.", detailsX + 20, detailsY + 70)
        end
    end
end

-- Draw options tab
function UISystem:drawOptionsTab()
    love.graphics.setFont(self.largeFont)
    love.graphics.setColor(0.9,0.9,0.9)
    love.graphics.print("Options", 50, 130)
    love.graphics.setFont(self.font)
    love.graphics.print("Fullscreen: " .. (Engine.settings.fullscreen and "ON" or "OFF"), 50, 170)
    love.graphics.print("Sound: " .. (Engine.settings.soundEnabled and "ON" or "OFF"), 50, 200)
    love.graphics.print("Music Volume: " .. (Engine.settings.musicVolume * 100) .. "%", 50, 230)
    love.graphics.print("SFX Volume: " .. (Engine.settings.sfxVolume * 100) .. "%", 50, 260)
    love.graphics.print("Performance Mode: " .. (Engine.settings.performanceMode and "ON" or "OFF"), 50, 290)

    -- Draw toggle/volume buttons and record their hitboxes
    local btnW, btnH = 80, 25
    local vsW = 25
    self.inGameMenu.optionButtons = {}
    self.inGameMenu.optionButtons.fullscreen = {x=300,y=170,w=btnW,h=btnH}
    love.graphics.setColor(0.3,0.3,0.3); love.graphics.rectangle("fill",300,170,btnW,btnH)
    love.graphics.setColor(1,1,1); love.graphics.print("Toggle",310,175)
    self.inGameMenu.optionButtons.sound = {x=300,y=200,w=btnW,h=btnH}
    love.graphics.setColor(0.3,0.3,0.3); love.graphics.rectangle("fill",300,200,btnW,btnH)
    love.graphics.setColor(1,1,1); love.graphics.print("Toggle",310,205)
    self.inGameMenu.optionButtons.musicDown = {x=300,y=230,w=vsW,h=vsW}
    love.graphics.setColor(0.3,0.3,0.3); love.graphics.rectangle("fill",300,230,vsW,vsW)
    love.graphics.setColor(1,1,1); love.graphics.print("-",308,235)
    self.inGameMenu.optionButtons.musicUp = {x=330,y=230,w=vsW,h=vsW}
    love.graphics.setColor(0.3,0.3,0.3); love.graphics.rectangle("fill",330,230,vsW,vsW)
    love.graphics.setColor(1,1,1); love.graphics.print("+",337,235)
    self.inGameMenu.optionButtons.sfxDown = {x=300,y=260,w=vsW,h=vsW}
    love.graphics.setColor(0.3,0.3,0.3); love.graphics.rectangle("fill",300,260,vsW,vsW)
    love.graphics.setColor(1,1,1); love.graphics.print("-",308,265)
    self.inGameMenu.optionButtons.sfxUp = {x=330,y=260,w=vsW,h=vsW}
    love.graphics.setColor(0.3,0.3,0.3); love.graphics.rectangle("fill",330,260,vsW,vsW)
    love.graphics.setColor(1,1,1); love.graphics.print("+",337,265)

    -- Performance mode toggle button
    self.inGameMenu.optionButtons.performanceMode = {x=300,y=290,w=btnW,h=btnH}
    love.graphics.setColor(0.3,0.3,0.3); love.graphics.rectangle("fill",300,290,btnW,btnH)
    love.graphics.setColor(1,1,1); love.graphics.print("Toggle",310,295)
end

-- NOTE: Debug tab has been removed from the in-game menu (ESC menu)
-- Debug functionality is now ONLY available in the F1 debug menu
-- This function is kept as a reference but is no longer used
--[[
function UISystem:drawDebugTab()
    -- Function removed - debug functionality should only be in F1 menu
end
--]]

-- Handle in-game menu mouse moves
function UISystem:handleInGameMenuMouseMove(x, y)
    if not self.inGameMenu or not self.inGameMenu.active then return end

    -- Check button hovers
    for _, button in pairs(self.inGameMenu.buttons) do
        button.hovered = x >= button.x and x <= button.x + button.width and
                          y >= button.y and y <= button.y + button.height
    end
end

-- Handle in-game menu clicks
function UISystem:handleInGameMenuClick(x, y)
    if not self.inGameMenu or not self.inGameMenu.active then return false end

    -- Check button clicks
    for _, button in pairs(self.inGameMenu.buttons) do
        if x >= button.x and x <= button.x + button.width and
           y >= button.y and y <= button.y + button.height then
            if button.action then
                button.action()
                return true
            end
        end
    end

    -- Check inventory slot clicks (only in inventory tab)
    if self.inGameMenu.currentTab == "inventory" then
        local player = self.inGameMenu.player
        local inventory = player.character and player.character.inventory or {}
        local items = inventory.items or {}

        -- Use the same grid constants as in drawInventoryTab
        local PANEL_X, PANEL_Y = 50, 170
        local SLOT = 50
        local GAP_X = 10
        local GAP_Y = 10
        local ROWS = 4
        local COLS = 8

        -- Function to convert grid position to pixel coordinates
        local function slotXY(r, c)
            local x = PANEL_X + c * (SLOT + GAP_X)
            local y = PANEL_Y + r * (SLOT + GAP_Y)
            return x, y
        end

        -- Check if click is within any inventory slot
        for i = 1, ROWS * COLS do
            local row = math.floor((i-1) / COLS)
            local col = (i-1) % COLS
            local slotX, slotY = slotXY(row, col)

            if x >= slotX and x <= slotX + SLOT and
               y >= slotY and y <= slotY + SLOT then
                self.inGameMenu.selectedItem = items[i]
                if items[i] then
                    self:addMessage("Selected " .. (items[i].name or "Unknown Item"), 1)
                else
                    self:addMessage("Empty inventory slot", 1)
                end
                return true
            end
        end
    end

    -- Check equip button click in inventory tab
    if self.inGameMenu.currentTab == "inventory" and self.inGameMenu.equipButton then
        local btn = self.inGameMenu.equipButton
        if x >= btn.x and x <= btn.x + btn.width and
           y >= btn.y and y <= btn.y + btn.height then
            -- Get player data
            local player = self.inGameMenu.player
            local charData = player.character or player
            local inventory = charData.inventory or {}
            local items = inventory.items or {}
            local equipment = charData.equipment or {}

            -- Get item data
            local itemId = btn.itemId
            local itemData = btn.itemData

            -- Determine which slot to equip to
            local slotToEquip = nil

            -- If equipment system is available, use it to determine valid slots
            if Engine.systems.equipment and Engine.systems.equipment.getValidSlots then
                local validSlots = Engine.systems.equipment.getValidSlots(itemData)
                if #validSlots > 0 then
                    slotToEquip = validSlots[1] -- Use first valid slot
                end
            else
                -- Simple fallback based on equipment type
                if itemData.equipmentType == "weapon" then
                    slotToEquip = "mainhand"
                elseif itemData.equipmentType == "armor" then
                    if itemData.equipmentSubType == "head" then
                        slotToEquip = "head"
                    elseif itemData.equipmentSubType == "chest" then
                        slotToEquip = "chest"
                    elseif itemData.equipmentSubType == "legs" then
                        slotToEquip = "legs"
                    elseif itemData.equipmentSubType == "feet" then
                        slotToEquip = "feet"
                    elseif itemData.equipmentSubType == "hands" then
                        slotToEquip = "hands"
                    end
                elseif itemData.equipmentType == "accessory" then
                    if not equipment.accessory1 then
                        slotToEquip = "accessory1"
                    else
                        slotToEquip = "accessory2"
                    end
                elseif itemData.equipmentType == "shield" then
                    slotToEquip = "offhand"
                end
            end

            if slotToEquip then
                -- Check if there's already an item in that slot
                local oldItemId = equipment[slotToEquip]

                -- Equip the new item
                if Engine.systems.equipment and Engine.systems.equipment.equipItem then
                    -- Use equipment system if available
                    Engine.systems.equipment.equipItem(charData, itemId, slotToEquip)
                else
                    -- Simple fallback
                    charData.equipment[slotToEquip] = itemId

                    -- Remove from inventory
                    for i, item in ipairs(items) do
                        if item.id == itemId then
                            if item.count and item.count > 1 then
                                item.count = item.count - 1
                            else
                                table.remove(items, i)
                            end
                            break
                        end
                    end

                    -- If there was an old item, add it to inventory
                    if oldItemId and charData.inventory and charData.inventory.items then
                        -- Get old item data if possible
                        local oldItemName = oldItemId
                        local oldItemData = nil
                        if Engine.systems.itemDatabase and Engine.systems.itemDatabase.getItem then
                            oldItemData = Engine.systems.itemDatabase.getItem(oldItemId)
                        elseif Engine.systems.equipment and Engine.systems.equipment.load then
                            oldItemData = Engine.systems.equipment.load(oldItemId)
                        end

                        if oldItemData and oldItemData.name then
                            oldItemName = oldItemData.name
                        end

                        table.insert(charData.inventory.items, {
                            id = oldItemId,
                            name = oldItemName,
                            count = 1
                        })
                    end
                end

                -- Clear selection and button
                self.inGameMenu.selectedItem = nil
                self.inGameMenu.equipButton = nil

                -- Format slot name for display
                local slotName = slotToEquip:gsub("^%l", string.upper)  -- Capitalize first letter
                if slotToEquip == "mainhand" then slotName = "Main Hand"
                elseif slotToEquip == "offhand" then slotName = "Off Hand"
                elseif slotToEquip == "ring1" then slotName = "Ring 1"
                elseif slotToEquip == "ring2" then slotName = "Ring 2"
                elseif slotToEquip == "accessory1" then slotName = "Accessory 1"
                elseif slotToEquip == "accessory2" then slotName = "Accessory 2"
                end

                -- Get item name for better messaging
                local itemName = "Unknown Item"
                if itemData and itemData.name then
                    itemName = itemData.name
                end

                self:addMessage("Equipped " .. itemName .. " to " .. slotName, 2)

                -- Play equip sound if available
                if Engine.systems.audio and Engine.systems.audio.playSound then
                    Engine.systems.audio.playSound("equip")
                end
            else
                self:addMessage("Cannot determine equipment slot", 2)
            end

            return true
        end
    end

    -- Check equipment slot clicks (only in equipment tab)
    if self.inGameMenu.currentTab == "equipment" and self.inGameMenu.equipmentSlots then
        for slotId, slot in pairs(self.inGameMenu.equipmentSlots) do
            if x >= slot.x and x <= slot.x + slot.width and
               y >= slot.y and y <= slot.y + slot.height then
                -- Select this equipment item
                if slot.itemId then
                    self.inGameMenu.selectedEquipment = slot.itemId

                    -- Get item name if possible
                    local itemName = "Unknown"
                    local itemData = nil
                    if Engine.systems.itemDatabase and Engine.systems.itemDatabase.getItem then
                        itemData = Engine.systems.itemDatabase.getItem(slot.itemId)
                    elseif Engine.systems.equipment and Engine.systems.equipment.load then
                        itemData = Engine.systems.equipment.load(slot.itemId)
                    end

                    if itemData and itemData.name then
                        itemName = itemData.name
                    end

                    -- Format slot name for display
                    local slotName = slotId:gsub("^%l", string.upper)  -- Capitalize first letter
                    if slotId == "mainhand" then slotName = "Main Hand"
                    elseif slotId == "offhand" then slotName = "Off Hand"
                    elseif slotId == "ring1" then slotName = "Ring 1"
                    elseif slotId == "ring2" then slotName = "Ring 2"
                    elseif slotId == "accessory1" then slotName = "Accessory 1"
                    elseif slotId == "accessory2" then slotName = "Accessory 2"
                    end

                    self:addMessage("Selected " .. itemName .. " (" .. slotName .. ")", 1)
                else
                    self.inGameMenu.selectedEquipment = nil

                    -- Format slot name for display
                    local slotName = slotId:gsub("^%l", string.upper)  -- Capitalize first letter
                    if slotId == "mainhand" then slotName = "Main Hand"
                    elseif slotId == "offhand" then slotName = "Off Hand"
                    elseif slotId == "ring1" then slotName = "Ring 1"
                    elseif slotId == "ring2" then slotName = "Ring 2"
                    elseif slotId == "accessory1" then slotName = "Accessory 1"
                    elseif slotId == "accessory2" then slotName = "Accessory 2"
                    end

                    self:addMessage("Empty " .. slotName .. " slot", 1)
                end
                return true
            end
        end

        -- Check unequip button click
        if self.inGameMenu.unequipButton and self.inGameMenu.selectedEquipment then
            local btn = self.inGameMenu.unequipButton
            if x >= btn.x and x <= btn.x + btn.width and
               y >= btn.y and y <= btn.y + btn.height then
                -- Find which slot has this item
                local player = self.inGameMenu.player
                local charData = player.character or player
                local equipment = charData.equipment or {}
                local slotToUnequip = nil

                for slot, itemId in pairs(equipment) do
                    if itemId == self.inGameMenu.selectedEquipment then
                        slotToUnequip = slot
                        break
                    end
                end

                if slotToUnequip then
                    -- Get item data for better messaging
                    local itemName = "Unknown Item"
                    local itemData = nil
                    if Engine.systems.itemDatabase and Engine.systems.itemDatabase.getItem then
                        itemData = Engine.systems.itemDatabase.getItem(self.inGameMenu.selectedEquipment)
                    elseif Engine.systems.equipment and Engine.systems.equipment.load then
                        itemData = Engine.systems.equipment.load(self.inGameMenu.selectedEquipment)
                    end

                    if itemData and itemData.name then
                        itemName = itemData.name
                    end

                    -- Format slot name for display
                    local slotName = slotToUnequip:gsub("^%l", string.upper)  -- Capitalize first letter
                    if slotToUnequip == "mainhand" then slotName = "Main Hand"
                    elseif slotToUnequip == "offhand" then slotName = "Off Hand"
                    elseif slotToUnequip == "ring1" then slotName = "Ring 1"
                    elseif slotToUnequip == "ring2" then slotName = "Ring 2"
                    elseif slotToUnequip == "accessory1" then slotName = "Accessory 1"
                    elseif slotToUnequip == "accessory2" then slotName = "Accessory 2"
                    end

                    -- Unequip the item
                    if Engine.systems.equipment and Engine.systems.equipment.unequipItem then
                        -- Use equipment system if available
                        Engine.systems.equipment.unequipItem(charData, slotToUnequip)
                    else
                        -- Simple fallback
                        charData.equipment[slotToUnequip] = nil

                        -- Add to inventory if possible
                        if charData.inventory and charData.inventory.items then
                            table.insert(charData.inventory.items, {
                                id = self.inGameMenu.selectedEquipment,
                                name = itemName,
                                count = 1
                            })
                        end
                    end

                    -- Clear selection
                    self.inGameMenu.selectedEquipment = nil
                    self:addMessage("Unequipped " .. itemName .. " from " .. slotName, 2)

                    -- Play unequip sound if available
                    if Engine.systems.audio and Engine.systems.audio.playSound then
                        Engine.systems.audio.playSound("unequip")
                    end
                end

                return true
            end
        end
    end

    -- Handle click on options toggles
    if self.inGameMenu.currentTab == "options" then
        local b = self.inGameMenu.optionButtons
        -- Fullscreen toggle
        if x>=b.fullscreen.x and x<=b.fullscreen.x+b.fullscreen.w and y>=b.fullscreen.y and y<=b.fullscreen.y+b.fullscreen.h then
            Engine.settings.fullscreen = not Engine.settings.fullscreen
            love.window.setFullscreen(Engine.settings.fullscreen)
            self:addMessage("Fullscreen " .. (Engine.settings.fullscreen and "Enabled" or "Disabled"), 2)
            return true
        end
        -- Sound toggle
        if x>=b.sound.x and x<=b.sound.x+b.sound.w and y>=b.sound.y and y<=b.sound.y+b.sound.h then
            Engine.settings.soundEnabled = not Engine.settings.soundEnabled
            Engine.systems.soundSystem.setEnabled(Engine.settings.soundEnabled)
            self:addMessage("Sound " .. (Engine.settings.soundEnabled and "On" or "Off"), 2)
            return true
        end
        -- Music volume down
        if x>=b.musicDown.x and x<=b.musicDown.x+b.musicDown.w and y>=b.musicDown.y and y<=b.musicDown.y+b.musicDown.h then
            Engine.settings.musicVolume = math.max(0, Engine.settings.musicVolume - 0.1)
            Engine.systems.soundSystem.setVolumes(Engine.settings.musicVolume, Engine.settings.sfxVolume)
            self:addMessage("Music Volume " .. math.floor(Engine.settings.musicVolume*100) .. "%", 2)
            return true
        end
        -- Music volume up
        if x>=b.musicUp.x and x<=b.musicUp.x+b.musicUp.w and y>=b.musicUp.y and y<=b.musicUp.y+b.musicUp.h then
            Engine.settings.musicVolume = math.min(1, Engine.settings.musicVolume + 0.1)
            Engine.systems.soundSystem.setVolumes(Engine.settings.musicVolume, Engine.settings.sfxVolume)
            self:addMessage("Music Volume " .. math.floor(Engine.settings.musicVolume*100) .. "%", 2)
            return true
        end
        -- SFX volume down
        if x>=b.sfxDown.x and x<=b.sfxDown.x+b.sfxDown.w and y>=b.sfxDown.y and y<=b.sfxDown.y+b.sfxDown.h then
            Engine.settings.sfxVolume = math.max(0, Engine.settings.sfxVolume - 0.1)
            Engine.systems.soundSystem.setVolumes(Engine.settings.musicVolume, Engine.settings.sfxVolume)
            self:addMessage("SFX Volume " .. math.floor(Engine.settings.sfxVolume*100) .. "%", 2)
            return true
        end
        -- SFX volume up
        if x>=b.sfxUp.x and x<=b.sfxUp.x+b.sfxUp.w and y>=b.sfxUp.y and y<=b.sfxUp.y+b.sfxUp.h then
            Engine.settings.sfxVolume = math.min(1, Engine.settings.sfxVolume + 0.1)
            Engine.systems.soundSystem.setVolumes(Engine.settings.musicVolume, Engine.settings.sfxVolume)
            self:addMessage("SFX Volume " .. math.floor(Engine.settings.sfxVolume*100) .. "%", 2)
            return true
        end

        -- Performance mode toggle
        if x>=b.performanceMode.x and x<=b.performanceMode.x+b.performanceMode.w and y>=b.performanceMode.y and y<=b.performanceMode.y+b.performanceMode.h then
            -- Toggle performance mode
            Engine.settings.performanceMode = not (Engine.settings.performanceMode or false)

            -- Apply performance settings to renderer
            if Engine.systems.renderer then
                -- Update renderer settings based on performance mode
                Engine.systems.renderer.enableShaders = not Engine.settings.performanceMode
                Engine.systems.renderer.enableBlur = false -- Always disable blur for better performance
                Engine.systems.renderer.simplifiedTiles = Engine.settings.performanceMode
                Engine.systems.renderer.simplifiedEntities = Engine.settings.performanceMode
            end

            self:addMessage("Performance Mode " .. (Engine.settings.performanceMode and "Enabled" or "Disabled"), 2)
            return true
        end
    end

    -- NOTE: Debug tab has been removed from the in-game menu (ESC menu)
    -- Debug functionality is now ONLY available in the F1 debug menu

    return false
end

-- Add message to display queue
function UISystem:addMessage(text, duration)
    if not self.messages then
        self.messages = {}
    end

    table.insert(self.messages, {
        text = text,
        duration = duration or 3,
        current = 0
    })
end

-- Debug function to check UI element z-order and clickability
function UISystem:debugUIElements(clickX, clickY)
    print("=== UI Elements Debug ===")

    -- Check if character creation is active
    if self.characterCreation.active then
        print("Character Creation is active")

        -- Debug tab buttons
        print("Tab Buttons:")
        for _, tabName in ipairs(self.characterCreation.tabs) do
            local button = self.characterCreation.buttons[tabName]
            if button then
                local isClicked = clickX and clickY and
                    clickX >= button.x and clickX <= button.x + button.width and
                    clickY >= button.y and clickY <= button.y + button.height

                print(string.format("  - %s: x=%d, y=%d, w=%d, h=%d, hovered=%s, clicked=%s",
                    tabName, button.x, button.y, button.width, button.height,
                    button.hovered and "true" or "false",
                    isClicked and "YES" or "no"))
            end
        end

        -- Debug form buttons
        local form = self.components.forms.characterCreation
        if form and form.buttons then
            print("Form Buttons:")
            for buttonName, btn in pairs(form.buttons) do
                local isClicked = clickX and clickY and
                    clickX >= btn.x and clickX <= btn.x + btn.width and
                    clickY >= btn.y and clickY <= btn.y + btn.height

                print(string.format("  - %s: x=%d, y=%d, w=%d, h=%d, hovered=%s, clicked=%s",
                    buttonName, btn.x, btn.y, btn.width, btn.height,
                    btn.hovered and "true" or "false",
                    isClicked and "YES" or "no"))
            end
        end

        -- Debug appearance buttons
        if self.characterCreation.appearanceButtons then
            print("\nAppearance Buttons:")
            local count = 0
            for id, btn in pairs(self.characterCreation.appearanceButtons) do
                count = count + 1
                local isClicked = clickX and clickY and
                    clickX >= btn.x and clickX <= btn.x + btn.width and
                    clickY >= btn.y and clickY <= btn.y + btn.height

                print(string.format("  - %s: x=%d, y=%d, w=%d, h=%d, clicked=%s",
                    id, btn.x, btn.y, btn.width, btn.height,
                    isClicked and "YES" or "no"))
            end
            print("Total appearance buttons: " .. count)
        end
    end

    print("========================")
end

-- Text input handler for character creation
function UISystem:textInput(text)
    if self.characterCreation and self.characterCreation.active then
        local form = self.components.forms.characterCreation
        if form and form.fields and form.fields.name then
            local nf = form.fields.name
            nf.value = nf.value .. text
            self.characterCreation.data.name = nf.value
        end
    end
end

-- Focus management for input fields
function UISystem:handleTextInputFocus()
    if self.characterCreation and self.characterCreation.active then
        local form = self.components.forms.characterCreation
        if form and form.fields and form.fields.name then
            -- Simulate focus on name field
            form.fields.name.focused = true
        end
    end
end

function UISystem:loadPlayerData()
    -- Load saved character JSONs from absolute path
    local saveDir = "C:/Users/<USER>/Downloads/lazarus arc/love-11.5-win64/"
    local temp = {}
    for i = 1, 4 do
        local path = saveDir .. "player_" .. i .. ".json"
        local f = io.open(path, "r")
        if f then
            local content = f:read("*a")
            f:close()
            local ok, data = pcall(lunajson.decode, content)
            if ok and type(data) == "table" then
                table.insert(temp, data)
            end
        end
    end
    -- Store all local JSON characters
    self.mainMenu.allLocal = temp
    -- Populate initial slots with local characters
    for i = 1, math.min(4, #temp) do
        local slot = self.mainMenu.playerSlots[i]
        local character = temp[i]
        slot.empty = false
        slot.source = "json"
        slot.jsonIndex = i
        slot.name = character.name or slot.name
        slot.level = character.level or slot.level
        slot.class = character.class or slot.class
        slot.character = character
    end
    -- Fallback: fill remaining slots with DB characters if available
    if #temp < 4 and Engine.systems.databaseManager and Engine.systems.databaseManager.getAllCharacters then
        local saved = Engine.systems.databaseManager.getAllCharacters() or {}
        for i = #temp + 1, math.min(4, #saved) do
            local slot = self.mainMenu.playerSlots[i]
            local character = saved[i]
            slot.empty = false
            slot.source = "db"
            slot.name = character.name or slot.name
            slot.level = character.level or slot.level
            slot.class = character.class or slot.class
            slot.character = character
        end
    end
end

-- Handle mouse wheel
function UISystem:wheelmoved(x, y)
    -- Let event log viewer handle wheel events
    if EventLogViewer.wheelMoved(x, y) then
        return true
    end

    return false
end

-- Handle text input for debug systems
function UISystem:textinput(text)
    -- Let event log viewer handle text input
    EventLogViewer.textInput(text)
end

return UISystem
