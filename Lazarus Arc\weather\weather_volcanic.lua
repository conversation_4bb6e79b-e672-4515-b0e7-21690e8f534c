-- weather/volcanic.lua
-- Volcanic weather pattern - ash clouds, lava rain, and toxic gases

local VolcanicWeather = {}
VolcanicWeather.__index = VolcanicWeather

-- Basic properties
VolcanicWeather.id = "volcanic"
VolcanicWeather.name = "Volcanic Activity"

-- Visual properties
VolcanicWeather.visual = {
    skyColor = {r = 100, g = 50, b = 30}, -- Dark orange-red
    sunIntensity = 0.4,
    cloudCoverage = 1.0,
    cloudColor = {r = 80, g = 40, b = 20}, -- Ash clouds
    ambientLightLevel = 0.5,
    ashColor = {r = 60, g = 60, b = 60} -- Ash particles
}

-- Environment modifiers
VolcanicWeather.environment = {
    temperature = 1.5, -- Very hot
    visibility = 0.2, -- Extremely poor visibility
    humidity = 0.3, -- Very dry
    windStrength = 0.7, -- Strong wind
    toxicity = 0.9, -- High toxic gas levels
    ashDensity = 0.8 -- High ash concentration
}

-- Particle systems
VolcanicWeather.particles = {
    ashClouds = {
        enabled = true,
        intensity = 0.9,
        color = {r = 60, g = 60, b = 60},
        size = {min = 2, max = 5},
        speed = {min = 100, max = 200}
    },
    lavaRain = {
        enabled = true,
        intensity = 0.6,
        color = {r = 255, g = 100, b = 0},
        size = {min = 3, max = 6},
        speed = {min = 300, max = 400}
    },
    toxicGas = {
        enabled = true,
        intensity = 0.7,
        color = {r = 100, g = 255, b = 100},
        size = {min = 1, max = 3},
        speed = {min = 50, max = 100}
    }
}

-- Sound effects
VolcanicWeather.sounds = {
    ambient = "volcanic_rumble",
    eruption = "volcanic_eruption",
    lavaImpact = "lava_splash",
    gasHiss = "toxic_gas",
    volume = 0.9
}

-- Effects on game entities and tiles
VolcanicWeather.effects = {
    -- Damage to exposed entities
    exposureDamage = 8, -- High damage per second
    
    -- Equipment damage
    equipmentDamageRate = 2.0,
    
    -- Movement effects
    movementSpeedMultiplier = 0.5,
    
    -- Vision effects
    visionRange = 0.2,
    
    -- Toxic effects
    toxicDamage = 5,
    
    -- Heat effects
    heatDamage = 3,
    
    -- Ash effects
    ashClogChance = 0.3, -- Chance for mechanical devices to clog
}

-- Transition probabilities to other weather (per game hour)
VolcanicWeather.transitions = {
    clear = 0.1,
    cloudy = 0.2,
    acid_rain = 0.1,
    -- Implied: 0.6 chance to stay volcanic
}

-- Day/night cycle modifiers
VolcanicWeather.timeModifiers = {
    dawn = {
        skyColor = {r = 120, g = 60, b = 40},
        ambientLightLevel = 0.4
    },
    day = {
        -- Uses default properties
    },
    dusk = {
        skyColor = {r = 90, g = 45, b = 25},
        ambientLightLevel = 0.3
    },
    night = {
        skyColor = {r = 40, g = 20, b = 10},
        ambientLightLevel = 0.2,
        lavaIntensity = 1.2 -- More visible lava at night
    }
}

-- Helper function to deep copy a table
local function deepCopy(orig)
    local copy
    if type(orig) == 'table' then
        copy = {}
        for orig_key, orig_value in next, orig, nil do
            copy[deepCopy(orig_key)] = deepCopy(orig_value)
        end
        setmetatable(copy, deepCopy(getmetatable(orig)))
    else
        copy = orig
    end
    return copy
end

-- Create a new instance of the weather pattern
function VolcanicWeather.new()
    local instance = setmetatable({}, VolcanicWeather)
    -- Copy all properties
    for k, v in pairs(VolcanicWeather) do
        if k ~= "__index" and k ~= "new" and type(v) ~= "function" then
            instance[k] = deepCopy(v)
        end
    end
    return instance
end

-- Initialize the weather pattern
function VolcanicWeather:init(world)
    -- Handle the case where init is called directly on the module
    if not world and type(self) ~= "table" then
        -- world parameter was passed as self
        world = self
        self = VolcanicWeather
    end
    
    if not world then
        print("Warning: VolcanicWeather:init called without world parameter")
        return self
    end
    
    print("Initializing volcanic weather")
    
    -- Set global light level
    if self.visual and self.visual.ambientLightLevel then
        world.lightLevel = self.visual.ambientLightLevel
    end
    
    -- Start ambient sounds
    if self.sounds and self.sounds.ambient then
        -- Play ambient volcanic sound
        print("Playing sound: " .. self.sounds.ambient)
    end
    
    -- Create particle systems
    if self.particles and self.particles.ashClouds and self.particles.ashClouds.enabled then
        -- Create ash cloud particles
        print("Creating ash cloud particles with intensity: " .. self.particles.ashClouds.intensity)
    end
    
    if self.particles and self.particles.lavaRain and self.particles.lavaRain.enabled then
        -- Create lava rain particles
        print("Creating lava rain particles with intensity: " .. self.particles.lavaRain.intensity)
    end
    
    if self.particles and self.particles.toxicGas and self.particles.toxicGas.enabled then
        -- Create toxic gas particles
        print("Creating toxic gas particles with intensity: " .. self.particles.toxicGas.intensity)
    end
    
    -- Display warning message to players
    if world.messageSystem then
        world.messageSystem:broadcast("WARNING: Volcanic activity detected. Extreme danger. Seek shelter immediately.")
    end
    
    return self
end

-- Update function called every frame
function VolcanicWeather:update(world, dt)
    -- Handle the case where update is called directly on the module
    if not dt and type(world) == "number" then
        -- Parameters were passed as (self, world, dt)
        dt = world
        world = self
        self = VolcanicWeather
    end
    
    if not world then return end
    
    -- Update day/night cycle based on world time
    local timeOfDay = world.timeOfDay or "day"
    local modifiers = self.timeModifiers and self.timeModifiers[timeOfDay]
    
    if modifiers then
        -- Apply time-specific modifiers
        if modifiers.skyColor then
            -- Change sky color
        end
        
        if modifiers.ambientLightLevel then
            world.lightLevel = modifiers.ambientLightLevel
        end
        
        if modifiers.lavaIntensity and self.particles and self.particles.lavaRain then
            self.particles.lavaRain.intensity = modifiers.lavaIntensity
        end
    end
    
    -- Apply effects to game world
    if world.entitySystem and world.entitySystem.entities then
        for id, entity in pairs(world.entitySystem.entities) do
            -- Check if entity is exposed to volcanic effects
            if entity.isExposed then
                -- Apply combined damage
                if entity.health and self.effects then
                    entity.health = entity.health - 
                        ((self.effects.exposureDamage + 
                          self.effects.toxicDamage + 
                          self.effects.heatDamage) * dt)
                end
                
                -- Apply equipment damage
                if entity.equipment and self.effects then
                    for _, item in pairs(entity.equipment) do
                        if item.durability then
                            item.durability = item.durability - 
                                (self.effects.equipmentDamageRate * dt)
                        end
                    end
                end
                
                -- Apply movement speed reduction
                if entity.speed and self.effects then
                    entity.speed = entity.speed * self.effects.movementSpeedMultiplier
                end
            end
            
            -- Special effects for different entity types
            if entity.categories and self.effects then
                -- Effect on mechanical devices
                if table.contains(entity.categories, "mechanical") then
                    if math.random() < self.effects.ashClogChance * dt then
                        if entity.onMalfunction then
                            entity:onMalfunction("ash_clog")
                        end
                    end
                end
                
                -- Effect on shields
                if table.contains(entity.categories, "shield") then
                    if entity.effectiveness then
                        entity.effectiveness = entity.effectiveness * 0.6 -- Severe shield reduction
                    end
                end
                
                -- Effect on sensors and vision devices
                if table.contains(entity.categories, "sensor") then
                    if entity.range then
                        entity.range = entity.range * self.effects.visionRange
                    end
                end
            end
        end
    end
    
    -- Random eruption effects
    if math.random() < 0.05 and self.sounds and self.sounds.eruption then
        -- Play eruption sound
        print("Playing sound: " .. self.sounds.eruption)
        -- Create eruption visual effect
    end
    
    -- Random lava impact sounds
    if math.random() < 0.1 and self.sounds and self.sounds.lavaImpact then
        -- Play lava impact sound
        print("Playing sound: " .. self.sounds.lavaImpact)
    end
    
    -- Random toxic gas hiss
    if math.random() < 0.15 and self.sounds and self.sounds.gasHiss then
        -- Play toxic gas sound
        print("Playing sound: " .. self.sounds.gasHiss)
    end
end

-- Clean up when weather changes
function VolcanicWeather:cleanUp(world)
    -- Handle the case where cleanUp is called directly on the module
    if type(self) ~= "table" then
        -- world parameter was passed as self
        world = self
        self = VolcanicWeather
    end
    
    if not world then return end
    
    -- Stop ongoing sounds and particle effects
    print("Volcanic weather ending")
    
    -- Notify players
    if world.messageSystem then
        world.messageSystem:broadcast("Volcanic activity decreasing. Air quality improving.")
    end
    
    -- Reset entity states
    if world.entitySystem and world.entitySystem.entities then
        for id, entity in pairs(world.entitySystem.entities) do
            -- Reset movement speed
            if entity.speed and entity.defaultSpeed then
                entity.speed = entity.defaultSpeed
            end
            
            -- Reset shield effectiveness
            if entity.categories and table.contains(entity.categories, "shield") then
                if entity.effectiveness and entity.defaultEffectiveness then
                    entity.effectiveness = entity.defaultEffectiveness
                end
            end
            
            -- Reset sensor ranges
            if entity.categories and table.contains(entity.categories, "sensor") then
                if entity.range and entity.defaultRange then
                    entity.range = entity.defaultRange
                end
            end
        end
    end
end

-- Table contains helper function
function table.contains(table, element)
    if not table then return false end
    
    for _, value in pairs(table) do
        if value == element then
            return true
        end
    end
    return false
end

return VolcanicWeather 