-- entities/rabbit.lua
local Rabbit = {
    id = "rabbit",
    name = "Rabbit",
    type = "rabbit",
    -- PLACEHOLDER: Basic shape for visual representation
    shape = {
        {0, -1}, {0.8, -0.6}, {1, 0}, {0.8, 0.6},
        {0, 1}, {-0.8, 0.6}, {-1, 0}, {-0.8, -0.6}
    },
    size = 6,

    -- Entity categories
    categories = {"animal", "prey", "mammal", "small"},
    
    -- Threat categories
    threatCategories = {"predator", "player"},
    
    -- Stats
    maxHealth = 20,
    health = 20,
    maxStamina = 40,
    stamina = 40,
    speed = 3.0,
    
    -- Behaviors
    behaviors = {"flee", "wander", "graze"},
    
    -- Behavior configurations
    behaviorConfigs = {
        flee = {
            moveSpeed = 4.0,
            detectionRadius = 10,
            panicDuration = 3,
            zigzagChance = 0.3
        },
        wander = {
            moveSpeed = 1.5,
            changeDirectionChance = 0.05,
            wanderRadius = 5
        },
        graze = {
            moveSpeed = 0.5,
            grazeRadius = 3,
            grazeTime = {5, 10}
        }
    },
    
    -- Special abilities
    abilities = {
        hop = {
            speedBoost = 1.5,
            duration = 0.5,
            cooldown = 2.0
        }
    },
    
    -- Appearance
    appearance = {
        sprite = "rabbit",
        scale = 0.8,
        animations = {
            "idle", "hop", "run", "graze"
        },
        variants = {
            "brown", "white", "gray", "black"
        }
    },
    
    -- Sound effects
    sounds = {
        hop = "rabbit_hop",
        squeak = "rabbit_squeak",
        footstep = "rabbit_footstep",
        hurt = "rabbit_hurt",
        death = "rabbit_death",
        idle = "rabbit_idle",
        alert = "rabbit_alert"
    },
    
    -- Loot drops
    drops = {
        {id = "meat", chance = 0.8, quantity = {1, 1}},
        {id = "hide", chance = 0.6, quantity = {1, 1}},
        {id = "foot", chance = 0.3, quantity = {1, 1}},
        {id = "fur", chance = 0.7, quantity = {1, 2}},
        {id = "bone", chance = 0.4, quantity = {1, 1}}
    }
}

-- Initialize the entity
function Rabbit.init(entity, world)
    -- Copy all fields from Rabbit template to entity instance
    for k, v in pairs(Rabbit) do
        if type(v) ~= "function" and entity[k] == nil then
            if type(v) == "table" then
                -- Deep copy for tables
                entity[k] = {}
                for subk, subv in pairs(v) do
                    entity[k][subk] = subv
                end
            else
                entity[k] = v
            end
        end
    end

    -- Set default position and velocity
    entity.position = entity.position or {x = 0, y = 0}
    entity.velocity = entity.velocity or {x = 0, y = 0}

    -- Initialize behaviors
    if world and world.modules and world.modules.behaviors then
        for _, behaviorName in ipairs(entity.behaviors) do
            local behavior = world.modules.behaviors[behaviorName]
            if behavior and behavior.init then
                behavior.init(entity, entity.behaviorConfigs[behaviorName])
            end
        end
    end

    -- Set random rabbit variant
    if entity.appearance and entity.appearance.variants then
        local variants = entity.appearance.variants
        entity.appearance.currentVariant = variants[math.random(#variants)]
    end

    return entity
end

return Rabbit
